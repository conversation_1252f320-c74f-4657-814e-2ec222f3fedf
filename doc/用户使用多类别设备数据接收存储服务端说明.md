# 用户使用多类别设备数据接收存储服务端说明

## 目录

1. [软件概述](#1-软件概述)
2. [新手快速上手](#2-新手快速上手)
3. [系统要求](#3-系统要求)
4. [安装与启动](#4-安装与启动)
5. [界面介绍](#5-界面介绍)
6. [配置管理](#6-配置管理)
7. [服务端运行](#7-服务端运行)
8. [数据管理](#8-数据管理)
9. [故障排除](#9-故障排除)
10. [注意事项](#10-注意事项)
11. [常见问题](#11-常见问题)

---

## 1. 软件概述

### 1.1 软件简介
本软件是一个基于FastAPI和tkinter的多类别设备数据接收存储服务端，专为局域网环境设计，支持多种设备的实时数据接收、存储和分发。

### 1.2 主要功能
- **数据接收**：通过HTTP接口接收来自局域网内各种设备的数据
- **身份验证**：支持基于客户端标识的设备认证和API密钥验证
- **数据存储**：存储接收到的数据，支持自动清理过期数据
- **实时监控**：提供图形界面实时显示接收到的数据和系统状态
- **多实例支持**：支持命令行和GUI两种运行模式，可同时运行多个实例
- **配置管理**：支持多配置文件管理，防止配置冲突

### 1.3 技术特性
- **跨平台支持**：支持Windows、Linux、macOS系统
- **多运行模式**：GUI界面、命令行、配置选择界面三种运行模式
- **实时监控**：CPU使用率、内存占用、进程状态等系统监控
- **数据安全**：IP白名单、设备标识验证、API密钥认证
- **自动化管理**：自动清理过期数据、日志文件，支持定时任务
- **用户友好**：直观的GUI界面，支持配置验证和错误提示

---

## 2. 新手快速上手

### 2.1 五分钟快速启动
如果您是第一次使用，按照以下步骤即可快速启动服务端：

#### 步骤1：启动程序
1. 双击 `NexusRecv.exe` 文件
2. 首次运行会直接进入主界面

#### 步骤2：基础配置
1. **设置API密钥**：在"API密钥"框中输入一个复杂密码（如：`MyServer2024Key`）
2. **设置端口**：保持默认端口 `8080`，或改为其他未占用端口
3. **设置运行时段**：输入 `00:00-00:00`（表示24小时运行）

#### 步骤3：添加设备标识
1. 在"发信设备标识"区域点击"新增"
2. 输入设备标识（如：`MyDevice001`）和描述（如：`测试设备`）
3. 点击"确定"保存

#### 步骤4：启动服务
1. 点击"启动服务端"按钮
2. 看到"服务状态：运行中"表示启动成功
3. 记录显示的服务地址（如：`http://*************:8080`）

#### 步骤5：测试连接
现在您的设备可以向服务地址发送数据，数据会在界面的表格中实时显示。

### 2.2 重要提醒
- **防火墙**：Windows首次运行时选择"允许访问"
- **网络**：确保发送数据的设备与服务端在同一局域网
- **配置保存**：配置会自动保存，下次启动可直接使用

---

## 3. 系统要求

### 3.1 硬件要求
- **CPU**：1GHz及以上处理器
- **内存**：2GB及以上
- **磁盘空间**：500MB及以上可用空间
- **网络**：局域网连接

### 3.2 软件要求
- **操作系统**：Windows 10/11, Linux (Ubuntu/CentOS), macOS
- **网络环境**：局域网环境，支持多设备通信

### 3.3 网络要求
- 发信设备必须与服务端处于同一局域网
- 确保防火墙允许配置的端口通信
- Windows系统首次运行可能需要允许防火墙访问

---

## 4. 安装与启动

### 4.1 可执行文件方式（推荐）
1. **下载软件**：获取`NexusRecv.exe`可执行文件
2. **首次启动**：双击`NexusRecv.exe`，首次运行会直接进入主界面
3. **后续启动**：再次启动会进入配置选择界面

### 4.2 开发者运行方式（可选）
如果您是开发者需要从源码运行：
```bash
# 1. 安装Python 3.11+和依赖
pip install -r requirements.txt

# 2. 启动配置选择界面
python src/config_selection_gui.py

# 3. 直接启动主界面（指定配置文件）
python src/webhook_server_gui.py --webhook_server.config <配置文件路径>

# 4. 命令行启动
python src/webhook_server_command.py --webhook_server.config <配置文件路径>
```

### 4.3 启动模式说明
- **配置选择界面**：管理多个配置文件，选择或创建配置（推荐方式）
- **主界面模式**：直接使用指定配置文件启动GUI界面
- **命令行模式**：无界面后台运行，适合服务器环境（开发者使用）

---

## 5. 界面介绍

### 5.1 配置选择界面
配置选择界面用于管理多个配置文件，包含以下功能：

#### 5.1.1 界面布局
```
+------------------------------------------------------+
|              配置选择                                 |
+------------------------------------------------------+
| [新增配置] [导入配置]  [清理无效] [刷新列表]               |
| 🟢 可用配置 (2个)                                     |
| ┌─────────────────────────────────────────────────┐  |
| │ ○ 生产环境配置    最后使用: 2小时前  [选择]           │  |
| │   📁 /path/to/production.ini                    │  |
| │ ○ 测试环境配置    最后使用: 1天前   [选择]           │  |
| │   📁 /path/to/testing.ini                       │  |
| └─────────────────────────────────────────────────┘  |
| 🔴 占用配置 (1个)                                      |
| 🔴 无效配置 (0个)                                      |
+------------------------------------------------------+
```

#### 5.1.2 配置状态说明
- **🟢 可用配置**：配置文件有效且未被占用，可以正常使用
- **🔴 占用配置**：配置文件正在被其他进程使用
- **⚠️ 无效配置**：配置文件损坏、被删除或格式错误

#### 5.1.3 操作按钮
- **新增配置**：创建新的配置文件并进入主界面

- **导入配置**：从文件系统选择现有配置文件导入

- **清理无效**：移除所有无效的配置记录

- **刷新列表**：重新检查所有配置文件的状态

**注意:**
> 如果对应的配置文件在外部进行修改,会导致无法启动服务端和下次的运行; 
如果真的需要手动外部修改配置文件,那么就需要在关闭当前程序之后修改对应配置文件然后再次打 开程序进入配置加载界面去除刚刚失效的配置文件,然后导入刚刚修改的配置文件 


### 5.2 主界面
主界面是软件的核心操作界面，包含以下区域：

#### 5.2.1 顶部通知栏
- 显示软件使用说明和配置提示
- 可点击"×"按钮关闭
- 滚动显示重要信息

#### 5.2.2 系统监控区域
- **CPU仪表盘**：实时显示CPU使用率
  - 绿色：<10%
  - 黄色：10-30%
  - 红色：>30%
- **服务状态信息**：
  - 服务端启动时间
  - 进程PID
  - 内存使用量（MB）

#### 5.2.3 配置管理区域
- **服务端配置**：API密钥、网络设置、运行时段等
- **发信设备标识**：管理允许发送数据的设备列表
- **实时验证**：配置项格式错误时显示红框提示

#### 5.2.4 实时数据表格
- 显示接收到的设备数据
- 支持双击行复制完整数据内容
- 自动滚动显示最新数据
- 数据为空时显示提示信息

#### 5.2.5 控制按钮区域
- **启动服务端**：启动数据接收服务
- **停止服务端**：停止服务并清理资源
- **网络修复**：Windows系统下的网络连接修复功能

---

## 6. 配置管理

### 6.1 服务端配置

#### 6.1.1 基础网络配置
- **主机地址(host)**：建议使用`0.0.0.0`监听所有网络接口
- **端口(port)**：服务监听端口，范围1-65535，确保端口未被占用
- **IP白名单(whitelist)**：
  - 使用`*`表示允许所有IP访问
  - 指定IP：`*************`
  - IP段：`***********/24`
  - 多个IP用逗号分隔：`*************,*************`

#### 6.1.2 安全配置
- **API密钥(api_key)**：
  - 长度必须超过10个字符
  - 只能包含字母和数字
  - 建议使用复杂的字符组合，类似密码
  - 用于获取访问令牌的身份验证

#### 6.1.3 运行时段配置
- **格式要求**：`HH:MM-HH:MM`（24小时制）
- **配置示例**：
  - `07:00-19:00`：每天7点到19点运行
  - `22:00-06:00`：跨天运行，从22点到次日6点
  - `00:00-00:00`：24小时不间断运行
- **注意事项**：
  - 当前时间不在运行时段内时无法启动服务
  - 开始时间和结束时间一致表示服务端全天运行
  - 开始时间大于结束时间表示支持跨天运行,服务端从今天的开始时间运行到明天的结束时间,如05:00-04:00就表示从今天的5点运行到明天的4点
  - 运行到结束时间时服务会自动停止

#### 6.1.4 数据管理配置
- **时区设置(time_zone)**：如`Asia/Shanghai`
- **数据过期天数(expire_data_days)**：
  - 已读数据的保存天数
  - 设置为0或负数表示永不过期
- **数据存储上限(data_limit_num)**：
  - 最大存储条数
  - 超出部分的已读数据会被自动删除

#### 6.1.5 日志配置
- **日志配置文件路径(log_config_path)**：
  - 必须是存在的`.ini`文件
  - 包含日志级别、文件路径、格式等配置
- **应用名称(app_name)**：用于日志标识

### 6.2 发信设备标识配置

#### 6.2.1 设备标识要求
- **标识格式**：
  - 只能包含字母和数字
  - 长度必须在10-30个字符之间
  - 区分大小写
  - 不能重复
- **描述信息**：
  - 不能少于2个字符
  - 用于标识区分不同的设备信息

#### 6.2.2 设备管理操作
- **新增设备**：添加新的发信设备标识
- **编辑设备**：修改现有设备的标识或描述
- **删除设备**：移除不需要的设备（至少保留一个）
- **批量操作**：支持批量删除多个设备

#### 6.2.3 配置验证
- 标识重复时无法保存
- 格式错误时显示红框提示
- 至少需要一个设备标识才能启动服务

---

## 7. 服务端运行

### 7.1 启动服务端

#### 7.1.1 启动前检查
1. **配置完整性**：确保所有配置项已填写

2. **格式验证**：所有配置项格式正确

   2.1  **API KEY格式要求**字符只能由数字和字母组成且长度限制

   2.2  **运行时段格式要求**是HH:MM-HH:MM,24小时制,不包含分钟

   2.3  **发信标识记录中要求**标识唯一,描述信息长度不能少于2个

   2.4  **标识格式要求**字符只能由数字和字母组成且长度限制

3. **运行时段**：当前时间在允许的运行时段内

4. **端口可用性**：指定端口未被其他程序占用

5. **设备标识**：至少配置一个发信设备标识记录

#### 7.1.2 启动过程
1. 点击"启动服务端"按钮
2. 系统进行配置验证
3. 启动HTTP服务监听指定端口
4. 初始化数据和定时任务
5. 显示服务状态信息

#### 7.1.3 启动成功标志
- 服务状态显示为"运行中"
- CPU仪表盘开始显示实时数据
- 显示服务PID和内存使用量
- 启动按钮变为不可用状态

### 7.2 服务端运行状态

#### 7.2.1 状态监控
- **运行时长**：自动计算并显示服务运行时间
- **CPU使用率**：实时监控服务端CPU占用
- **内存使用量**：显示当前内存占用（MB）
- **进程状态**：显示服务进程的PID

#### 7.2.2 数据接收
- **实时显示**：新接收的数据会立即在表格中显示
- **数据格式**：显示时间、设备标识、数据内容
- **数据复制**：双击表格行可复制完整数据内容
- **自动滚动**：表格自动滚动显示最新数据

#### 7.2.3 自动任务
服务端运行期间会自动执行以下任务：
- **令牌刷新**：定期刷新访问令牌
- **数据清理**：清理过期和超量的已读数据
- **状态检查**：监控系统资源使用情况
- **日志管理**：自动清理过期日志文件

### 7.3 停止服务端

#### 7.3.1 手动停止
1. 点击"停止服务端"按钮
2. 系统停止接收新数据
3. 清理运行中的定时任务
4. 关闭数据连接
5. 释放网络端口

#### 7.3.2 自动停止
- **运行时段结束**：到达配置的结束时间自动停止
- **异常情况**：发生严重错误时自动停止
- **资源不足**：系统资源不足时可能自动停止

#### 7.3.3 停止后状态
- 服务状态显示为"已停止"
- CPU仪表盘停止更新
- 清空状态信息显示
- 启动按钮恢复可用状态

---

## 8. 数据管理

### 8.1 数据存储

#### 8.1.1 存储位置
- **数据文件**：存储在用户目录下
- **日志文件**：按日期和级别分类存储
- **配置文件**：INI格式，支持UTF-8编码

#### 8.1.2 数据结构
- **消息数据**：存储接收到的设备数据
  - 时间戳：数据接收时间
  - 设备标识：发送数据的设备ID
  - 数据内容：实际的数据内容
  - 读取状态：标记数据是否已被读取

#### 8.1.3 数据安全
- **备份建议**：定期备份重要的数据文件
- **权限控制**：确保数据文件有适当的访问权限

### 8.2 数据清理

#### 8.2.1 自动清理策略
- **过期数据清理**：
  - 根据`expire_data_days`配置清理过期的已读数据
  - 未读数据不会被清理
  - 设置为0或负数表示永不过期

- **超量数据清理**：
  - 根据`data_limit_num`配置限制数据总量
  - 超出限制时优先删除已读数据
  - 保证未读数据不被误删

#### 8.2.2 手动清理
- **日志清理**：定期清理过期的日志文件
- **配置清理**：使用"清理无效"功能移除无效配置
- **数据维护**：定期检查数据完整性

---

## 9. 故障排除

### 9.1 启动问题

#### 9.1.1 配置文件错误
**问题现象**：启动时提示配置文件错误
**解决方法**：
1. 检查配置文件格式是否为标准INI格式
2. 确认所有必需配置项都已填写
3. 验证配置项值的格式是否正确
4. 确保文件编码为UTF-8

#### 9.1.2 端口被占用
**问题现象**：提示端口已被占用
**解决方法**：
1. 更换其他可用端口
2. 检查是否有其他程序占用该端口
3. 使用`netstat -an`命令查看端口使用情况
4. 重启计算机释放端口

#### 9.1.3 权限不足
**问题现象**：无法创建文件或访问目录
**解决方法**：
1. 以管理员身份运行程序
2. 检查文件和目录的访问权限
3. 确保有足够的磁盘空间
4. 检查防病毒软件是否阻止访问

### 9.2 网络通信问题

#### 9.2.1 防火墙阻止
**问题现象**：局域网设备无法访问服务
**解决方法**：
1. Windows系统：允许程序通过防火墙
2. 检查路由器防火墙设置
3. 使用"网络修复"功能（Windows）
4. 临时关闭防火墙测试连通性

#### 9.2.2 网络配置错误
**问题现象**：设备无法连接到服务端
**解决方法**：
1. 确认设备与服务端在同一局域网
2. 检查IP地址和端口配置
3. 使用ping命令测试网络连通性
4. 检查网络设备（路由器、交换机）状态

### 9.3 性能问题

#### 9.3.1 响应缓慢
**问题现象**：界面响应缓慢或数据处理延迟
**解决方法**：
1. 检查系统资源使用情况
2. 清理过期数据减少数据大小
3. 调整数据清理策略
4. 升级硬件配置

#### 9.3.2 内存占用过高
**问题现象**：程序内存使用量持续增长
**解决方法**：
1. 重启服务端释放内存
2. 调整数据存储上限
3. 检查是否有内存泄漏
4. 定期重启程序

### 9.4 数据问题

#### 9.4.1 数据丢失
**问题现象**：接收的数据没有保存或显示
**解决方法**：
1. 检查数据文件是否存在
2. 验证设备标识是否正确配置
3. 查看日志文件了解错误信息
4. 检查磁盘空间是否充足

#### 9.4.2 数据格式错误
**问题现象**：数据显示异常或乱码
**解决方法**：
1. 确认数据编码格式
2. 检查特殊字符处理
3. 验证数据长度是否超限
4. 查看发送端数据格式

---

## 10. 注意事项

### 10.1 使用前注意事项

#### 10.1.1 网络环境
- 确保发信设备与服务端处于同一局域网
- 检查网络防火墙设置，确保端口通信正常
- Windows系统首次运行时需要允许防火墙访问

#### 10.1.2 配置要求
- 服务端配置界面的API KEY只能由字母和数字组成,长度限制10-30
- 服务端配置界面的运行时段必须符合时间格式(HH:MM)

- 发信设备界面中设备标识只能由字母和数字组成且长度限制在10-30个字符
- 发信设备界面中每个设备标识必须唯一，不能重复

### 10.2 运行期间注意事项

#### 10.2.1 配置限制
- 服务端运行期间不能修改设置中的配置项
- 如需修改配置，必须先停止服务端
- 外部修改配置文件会导致配置失效

#### 10.2.2 数据操作
- 双击实时数据表格中的指定行可复制完整数据内容
- 已读的过期很久的数据会根据配置自动清理，注意及时备份重要数据
- 未读数据不会被自动清理

#### 10.2.3 多实例使用
- 同一个配置文件只能被一个进程使用
- 多个实例必须使用不同的配置文件
- 配置被占用时其他程序无法使用

### 10.3 数据安全注意事项

#### 10.3.1 敏感信息保护
- API密钥应使用复杂字符组合，类似密码强度
- 定期更换API密钥提高安全性
- 不要在不安全的环境中暴露配置文件

#### 10.3.2 数据备份
- 定期备份重要的数据文件
- 保存配置文件的副本
- 建立数据恢复计划

#### 10.3.3 访问控制
- 合理配置IP白名单限制访问
- 监控异常访问行为
- 及时更新和维护设备标识列表

---

## 11. 常见问题

### 11.1 安装和启动问题

**Q: 首次启动程序没有反应？**
A: 检查是否有杀毒软件阻止程序运行，尝试以管理员身份运行，或者添加程序到杀毒软件白名单。

**Q: 提示缺少配置项无法启动？**
A: 首次运行需要完整配置所有必需项，包括API密钥、网络设置、运行时段和至少一个发信设备标识。

**Q: 配置选择界面显示配置无效？**
A: 可能是配置文件被外部修改或损坏，使用"清理无效"功能移除无效配置，然后重新创建或导入配置。

### 11.2 网络连接问题

**Q: 局域网设备无法连接到服务端？**
A: 
1. 确认设备与服务端在同一局域网
2. 检查防火墙设置，允许程序通过防火墙
3. 验证IP地址和端口配置是否正确
4. 使用ping命令测试网络连通性

**Q: Windows防火墙总是弹出警告？**
A: 首次运行时选择"允许访问"，如果重复出现，可以使用"网络修复"功能重新配置防火墙规则。

### 11.3 配置和使用问题

**Q: 运行时段配置总是提示格式错误？**
A: 确保使用24小时制格式(HH:MM-HH:MM)，例如：07:00-19:00，不要包含秒数或其他字符。

**Q: 发信设备标识无法保存？**
A: 检查标识是否只包含字母和数字，长度在10-30字符之间，且不与现有标识重复。

**Q: 数据表格中看不到接收的数据？**
A: 
1. 确认服务端已正常启动
2. 检查发信设备的标识是否正确配置
3. 验证发信设备的网络连接
4. 查看日志文件了解详细错误信息

### 11.4 性能和稳定性问题

**Q: 程序运行一段时间后变慢？**
A: 可能是数据量过大，调整数据清理策略，减少数据存储上限，或者定期重启服务端。

**Q: 服务端自动停止运行？**
A: 
1. 检查是否到达配置的运行时段结束时间
2. 查看日志文件了解停止原因
3. 检查系统资源是否充足
4. 验证配置文件是否被外部修改

**Q: 如何备份和恢复数据？**
A:
1. 备份用户目录下的.webhook_server文件夹
2. 包含数据文件和配置文件
3. 恢复时将备份文件复制回原位置
4. 重新启动程序验证数据完整性

---

## 技术支持

如果遇到本文档未涵盖的问题，请：

1. **查看日志文件**：logs目录下的详细日志信息
2. **检查系统环境**：确认系统满足运行要求
3. **联系技术支持**：发送日志文件和问题描述到 <EMAIL>

---

**版本信息**：v1.0.1  
**更新日期**：2025-07-25  
**适用软件**：NexusRecv 多类别设备数据接收存储服务端
