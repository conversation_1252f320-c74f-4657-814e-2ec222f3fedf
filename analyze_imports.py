#!/usr/bin/env python3
"""
分析项目中的导入语句，找出需要替换的from...import语句
"""

import os
import re
import ast
from typing import Dict, List, Set, Tuple
from pathlib import Path

def find_python_files(src_dir: str) -> List[str]:
    """查找所有Python文件"""
    python_files = []
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    return python_files

def analyze_imports_in_file(file_path: str) -> Dict[str, List[str]]:
    """分析单个文件中的导入语句"""
    imports_info = {
        'from_imports': [],  # from module import item1, item2
        'regular_imports': [],  # import module
        'errors': []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析AST
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ImportFrom):
                # from module import item1, item2
                if node.module:  # 确保module不为None
                    items = []
                    for alias in node.names:
                        items.append(alias.name)
                    imports_info['from_imports'].append({
                        'module': node.module,
                        'items': items,
                        'line': node.lineno
                    })
            elif isinstance(node, ast.Import):
                # import module
                for alias in node.names:
                    imports_info['regular_imports'].append({
                        'module': alias.name,
                        'line': node.lineno
                    })
                    
    except Exception as e:
        imports_info['errors'].append(str(e))
    
    return imports_info

def should_convert_import(module_name: str) -> bool:
    """判断是否应该转换这个导入语句"""
    # 只转换项目内部的模块导入
    internal_modules = [
        'common.utils',
        'common.models', 
        'common.constants',
        'webhook_server.utils',
        'webhook_server.models',
        'webhook_server.config'
    ]
    
    for internal_mod in internal_modules:
        if module_name.startswith(internal_mod):
            return True
    return False

def main():
    """主函数"""
    src_dir = 'src'
    python_files = find_python_files(src_dir)
    
    print(f"找到 {len(python_files)} 个Python文件")
    print("=" * 80)
    
    all_from_imports = []
    
    for file_path in python_files:
        print(f"\n分析文件: {file_path}")
        imports_info = analyze_imports_in_file(file_path)
        
        if imports_info['errors']:
            print(f"  错误: {imports_info['errors']}")
            continue
            
        # 分析from imports
        for from_import in imports_info['from_imports']:
            module = from_import['module']
            items = from_import['items']
            line = from_import['line']
            
            if should_convert_import(module):
                print(f"  第{line}行: from {module} import {', '.join(items)}")
                all_from_imports.append({
                    'file': file_path,
                    'line': line,
                    'module': module,
                    'items': items
                })
    
    print("\n" + "=" * 80)
    print(f"总共找到 {len(all_from_imports)} 个需要转换的导入语句")
    
    # 按模块分组显示
    module_groups = {}
    for imp in all_from_imports:
        module = imp['module']
        if module not in module_groups:
            module_groups[module] = []
        module_groups[module].append(imp)
    
    print("\n按模块分组:")
    for module, imports in module_groups.items():
        print(f"\n{module}:")
        for imp in imports:
            print(f"  {imp['file']}:{imp['line']} -> {', '.join(imp['items'])}")

if __name__ == '__main__':
    main()
