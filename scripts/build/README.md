# NexusRecv WebHook Server PyInstaller 打包指南

本目录包含了用于打包NexusRecv WebHook Server GUI应用程序的所有脚本和配置文件。

## 文件说明

- `build_common.py` - 公共打包模块，包含Windows和Linux平台的公共代码和变量
- `build_by_platform.py` - 跨平台打包脚本，自动检测平台并选择合适的打包方式
- `build_windows.py` - Windows平台专用打包脚本
- `build_linux.py` - Linux平台专用打包脚本，支持生成deb包
- `requirements_build.txt` - 打包所需的依赖列表
- `test_build_env.py` - 环境测试脚本
- `README.md` - 本说明文件

## 快速开始

### 方法1: 跨平台自动打包（推荐）

```bash
# 1. 安装打包依赖
pip install -r scripts/build/requirements_build.txt

# 2. 自动检测平台并打包
python scripts/build/build_by_platform.py
```

### 方法2: 平台特定打包

#### Windows用户
```bash
# 安装依赖
pip install -r scripts/build/requirements_build.txt

# Windows平台打包
python scripts/build/build_windows.py
```

#### Linux用户
```bash
# 安装依赖
pip install -r scripts/build/requirements_build.txt

# Linux平台打包（包含deb包）
python scripts/build/build_linux.py
```

### 环境测试

在打包前，建议运行环境测试脚本：

```bash
python test_build_env.py
```

该脚本会检查：
- Python版本和路径
- 平台信息
- 关键文件存在性
- 依赖模块安装情况
- subprocess功能测试

## 项目结构

```
python-samples-hub/
├── src/                          # 源代码目录
│   ├── config_selection_gui.py   # 主入口文件
│   ├── webhook_server_gui.py     # 主界面
│   └── ...
├── resources/                    # 资源文件
│   ├── 22.png                   # 应用图标资源
│   ├── x.ico                    # Windows图标
│   ├── x.icns                   # macOS图标
│   └── x.png                    # Linux图标
├── scripts/build/               # 打包脚本目录
│   ├── build_common.py          # 公共打包模块
│   ├── build_by_platform.py     # 跨平台打包脚本
│   ├── build_windows.py         # Windows打包脚本
│   ├── build_linux.py           # Linux打包脚本
│   ├── requirements_build.txt   # 打包依赖
│   ├── test_build_env.py        # 环境测试脚本
│   └── README.md               # 本文档
├── webhook_server_gui.spec      # PyInstaller配置文件
```

## 平台特定说明

### Windows平台

**输出文件**:
- `release/windows/NexusRecv.exe` - 主程序（单文件模式）
- `release/windows/NexusRecv/` - 应用程序目录（目录模式）
- `release/windows/start.bat` - 启动脚本

**特性**:
- 自动检测单文件或目录模式
- 不显示控制台窗口
- 自动清理防火墙规则
- 包含所有依赖

**运行要求**:
- Windows 10或更高版本
- 无需安装Python环境

### Linux平台

**输出文件**:
- `release/linux/NexusRecv/` - 应用程序目录
- `release/linux/start.sh` - 启动脚本
- `release/nexusrecv-webhook-server_1.0.0_amd64.deb` - deb安装包

**deb包安装**:
```bash
sudo dpkg -i nexusrecv-webhook-server_1.0.0_amd64.deb
sudo apt-get install -f  # 解决依赖问题
nexusrecv-webhook-server  # 启动应用
```

**手动运行**:
```bash
cd release/linux
./start.sh
```

**运行要求**:
- Ubuntu 18.04+ / CentOS 7+ 或兼容发行版
- X11显示服务器
- 基本的系统库（libc6, libgcc-s1等）

## 打包配置

### 主要特性

1. **智能打包**: 自动检测单文件或目录模式
2. **资源文件**: 自动包含所有必要的图标和资源文件
3. **隐藏导入**: 自动处理所有必要的Python模块
4. **跨平台**: 支持Windows exe和Linux deb包
5. **GUI优化**: Windows版本不显示控制台窗口
6. **防火墙清理**: Windows版本自动清理防火墙规则

### spec文件配置

`webhook_server_gui.spec`文件包含了所有打包配置：

- **软件名称**: `NexusRecv`
- **入口文件**: `src/config_selection_gui.py`
- **资源文件**: `resources/22.png`, `x.ico`, `x.icns`, `x.png`
- **隐藏导入**: 包含requests, aiohttp, multiprocessing等必要模块
- **排除模块**: 排除unittest, pytest, numpy等测试和不必要的模块
- **平台适配**: 自动根据平台选择合适的图标和配置

## 自定义配置

### 添加资源文件

如果需要添加更多资源文件，编辑`webhook_server_gui.spec`:

```python
datas = [
    (str(project_root / "resources" / "22.png"), "resources"),
    (str(project_root / "resources" / "new_file.txt"), "resources"),
    # 添加更多文件...
]
```

### 添加隐藏导入

如果遇到导入错误，添加到`hiddenimports`列表:

```python
hiddenimports = [
    'requests', 'future', 'aiohttp', 'aiohttp.web',
    'logging.handlers', 'multiprocessing', 'multiprocessing.pool',
    'your_new_module',  # 添加新模块
]
```

### 修改应用图标

```python
# Windows图标
win_icon = str(project_root / "resources" / "your_icon.ico")

# Linux图标
linux_icon = str(project_root / "resources" / "your_icon.png")

# macOS图标
mac_icon = str(project_root / "resources" / "your_icon.icns")
```

## 故障排除

### 常见问题

1. **模块导入错误**
   ```
   ModuleNotFoundError: No module named 'xxx'
   ```
   解决方案: 将缺失的模块添加到`webhook_server_gui.spec`的`hiddenimports`列表

2. **资源文件缺失**
   ```
   FileNotFoundError: [Errno 2] No such file or directory: 'resources/22.png'
   ```
   解决方案: 检查`datas`列表中的文件路径，确保资源文件存在

3. **Linux显示问题**
   ```
   cannot connect to X server
   ```
   解决方案: 设置DISPLAY环境变量或启用X11转发
   ```bash
   export DISPLAY=:0.0
   ```

4. **权限问题**
   ```
   Permission denied
   ```
   解决方案: 给启动脚本添加执行权限
   ```bash
   chmod +x start.sh
   ```

5. **PyInstaller安装失败**
   ```
   pip install pyinstaller failed
   ```
   解决方案: 使用scripts/build中的requirements_build.txt
   ```bash
   pip install -r scripts/build/requirements_build.txt
   ```

### 调试方法

1. **运行环境测试**:
   ```bash
   python test_build_env.py
   ```

2. **启用调试模式**:
   ```bash
   python -m PyInstaller --debug=all webhook_server_gui.spec
   ```

3. **查看导入信息**:
   ```bash
   python -m PyInstaller --debug=imports webhook_server_gui.spec
   ```

4. **保留临时文件**:
   ```bash
   python -m PyInstaller --debug=noarchive webhook_server_gui.spec
   ```

## 高级选项

### UPX压缩

spec文件中已启用UPX压缩（如果系统中安装了UPX）:

```python
exe = EXE(
    # ...
    upx=True,
    upx_exclude=[],
    # ...
)
```

### 代码签名（Windows）

```python
exe = EXE(
    # ...
    codesign_identity="Your Certificate Name",
    # ...
)
```

## 版本管理

当发布新版本时，记得更新以下文件:

1. `scripts/build/build_linux.py` - deb包版本号
2. `webhook_server_gui.spec` - 软件名称和版本信息
3. Linux桌面文件版本信息

## 测试建议

1. **环境测试**: 运行`python test_build_env.py`检查环境
2. **本地测试**: 在干净的环境中测试打包后的应用
3. **虚拟机测试**: 在目标操作系统的虚拟机中测试
4. **依赖检查**: 确保目标系统有必要的系统库
5. **功能测试**: 验证所有功能正常工作

## 文件结构说明

打包完成后的文件结构：

```
release/
├── windows/                     # Windows发布文件
│   ├── NexusRecv.exe           # 主程序（单文件模式）
│   ├── NexusRecv/              # 或程序目录（目录模式）
│   └── start.bat               # 启动脚本
└── linux/                      # Linux发布文件
    ├── NexusRecv/              # 程序目录
    ├── start.sh                # 启动脚本
    └── nexusrecv-webhook-server_1.0.0_amd64.deb  # deb安装包
```

## 支持

如果遇到打包问题，请检查:

1. Python版本兼容性（推荐3.8+）
2. PyInstaller版本（推荐6.0.0+）
3. 运行环境测试脚本检查依赖
4. 系统依赖是否完整
5. 文件路径是否正确

更多信息请参考：
- PyInstaller官方文档: https://pyinstaller.readthedocs.io/
- 项目GitHub仓库
