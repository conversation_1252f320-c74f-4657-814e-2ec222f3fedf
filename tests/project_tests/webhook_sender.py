import asyncio
import concurrent
import random
import re
import string
import time

import aiohttp
import requests

from config import constants
from models import server_properties
from common.utils.path_utils import get_real_path_create_dir


# 中文字符池：在常用汉字 Unicode 范围（0x4E00–0x9FFF）内随机挑选
def random_chinese_char():
    # 常用汉字大致在 0x4E00 到 0x9FFF 之间
    return chr(random.randint(0x4E00, 0x9FFF))


# 随机生成一个长度为 n 的字符串，包含英文字符（大小写字母和数字）和中文汉字
def random_cn_en_string(n: int) -> str:
    if n <= 0:
        raise ValueError("参数 n 必须为大于 0 的整数")

    # 英文字符池：包含大小写字母和数字
    en_pool = string.ascii_letters + string.digits

    result_chars = []
    for _ in range(n):
        # 随机决定是取英文字符还是中文字符，比例可自行调整
        if random.random() < 0.5:
            # 取英文字符
            result_chars.append(random.choice(en_pool))
        else:
            # 取一个随机中文汉字
            result_chars.append(random_chinese_char())

    return ''.join(result_chars)


# 给client_id_set中每一个元素重复生成 random_cn_en_string 函数生成长度在5-10之间的随机字符串 message_count 次,拼接"client_id,当前生成的字符串"并将结果写入到 out_path 文件中
def generate_stress_test_data(client_id_set: set, message_count: int, out_path: str):
    if not client_id_set:
        raise ValueError("client_id_set 不能为空")
    if not isinstance(message_count, int) or message_count <= 0:
        raise ValueError("message_count 必须是大于 0 的整数")
    if not isinstance(out_path, str) or not out_path:
        raise ValueError("out_path 不能为空字符串")

    # 确保目录存在
    out_path = get_real_path_create_dir(out_path)

    # 写入文件（覆盖模式）
    with open(out_path, 'w', encoding='utf-8') as f:
        # 首行
        f.write("client_id,message\n")
        # 逐条写入
        for client_id in client_id_set:
            for _ in range(message_count):
                length = random.randint(5, 10)
                msg = random_cn_en_string(length)
                f.write(f"{client_id},{msg}\n")


# 根据客户端id文件路径获取所以的客户端id,然后使用generate_stress_test_data函数生成测试数据
def generate_stress_test_data_by_client_id_file(client_keys: set, message_count: int, out_path: str):
    if not isinstance(out_path, str) or not out_path:
        raise ValueError("out_path 不能为空字符串")

    client_id_set = set()
    # 读取客户端id文件
    for client_key in client_keys:
        if not re.match(r'^[a-zA-Z0-9]{10,14}$', client_key):
            raise ValueError(f"line {client_key}: invalid key format")
        if client_key in client_id_set:
            raise ValueError(f"line {client_key}: duplicate key")
        client_id_set.add(client_key)


    # 生成测试数据
    generate_stress_test_data(client_id_set, message_count, out_path)


send_data_api_url = 'http://localhost:8000/webhook/save'


def send_by_client_id_request(client_id: str, message: str):
    headers = {'x-client-key': f'{client_id}'}
    data = f'{{"content":"{message}"}}'
    response = requests.post(send_data_api_url, headers=headers, data=data)
    response_json = response.json()
    if response_json['status'] != 'success':
        print(f"response: {response_json}")


async def async_send_request(client_id: str, message: str):
    async with aiohttp.ClientSession() as session:
        headers = {
            'x-client-key': client_id,
            'Content-Type': 'application/json'  # 必须声明
        }
        data = f'{{"content":"{message}"}}'
        async with session.post(send_data_api_url, headers=headers, data=data) as response:
            response_json = await response.json()
            if response_json['status'] != 'success':
                print(f"response: {response_json}")


def async_send_by_client_id_file(client_keys: set, message_count: int, out_path: str):
    # 生成测试数据
    generate_stress_test_data_by_client_id_file(client_keys, message_count, out_path)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        with open(out_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i == 0:
                    continue
                client_id, message = line.strip().split(',')
                loop.run_until_complete(async_send_request(client_id, message))
    finally:
        loop.close()


def thread_send_by_client_id_file(client_keys: set, message_count: int, out_path: str):
    # 生成测试数据
    generate_stress_test_data_by_client_id_file(client_keys, message_count, out_path)
    data_list = []
    with open(out_path, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i == 0:
                continue
            client_id, message = line.strip().split(',')
            data_list.append((client_id, message))

    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        [executor.submit(send_by_client_id_request, client_id, message) for client_id, message in data_list]



# 表情符号集合（适量选取）
emojis = list("😀😁😂🤣😃😄😅😆😉😊😋😎😍😘🥰😗😙😚🙂🤗🤩🤔🤨🙄😏😣😥😮🤐😯😪😫😴😌😛😜😝🤤😒😓😔😕🙃🤑😲")

# 汉字样本（简体常用）
chinese_chars = list("的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞")

# 纯英文字符串生成
def generate_english_string(length=100):
    return ''.join(random.choices(string.ascii_letters, k=length))

# 纯中文字符串生成
def generate_chinese_string(length=100):
    return ''.join(random.choices(chinese_chars, k=length))

# 纯表情符号字符串生成
def generate_emoji_string(length=100):
    return ''.join(random.choices(emojis, k=length))

# 混合字符串生成（中英文+表情+符号）
def generate_mixed_string(length=100):
    mixed_pool = chinese_chars + list(string.ascii_letters) + emojis + list("~!@#$%^&*()_+-=[]{}|;:',.<>?/")
    return ''.join(random.choices(mixed_pool, k=length))

def send_by_client_id_request_(ip:str,port:str,client_id: str, message: str):
    headers = {'x-client-key': f'{client_id}'}
    data = f'{{"content":"{message}"}}'
    request_url=f'http://{ip}:{port}/webhook/save' # noqa
    try:
        response = requests.post(request_url, headers=headers, data=data)
        response_json = response.json()
        status=response_json.get('status')
        if status != 'success':
            print(f"response: {response_json}")
    except BaseException as e:
        print(f"send_by_client_id_request_ error: {e}")


def generate_randomized_concurrency_test(ip:str, port:str, client_keys:list, all_message_num):
    """随机生成发信内容进行发信请求的并发测试"""
    data_list=[]
    for _ in range(all_message_num):
        first = random.choice(client_keys)
        second = random.choice([
            generate_english_string,
            generate_chinese_string,
            generate_emoji_string,
            generate_mixed_string
        ])()
        data_list.append((first, second))
    print(f"共生成{len(data_list)}条数据")

    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        futures = [executor.submit(send_by_client_id_request_, ip, port, client_id, message) for client_id, message in data_list]
        for future in futures:
            try:
                future.result()  # 可以在这里加异常捕获以防请求出错
            except Exception as e:
                print(f"Request error: {e}")



if __name__ == '__main__':
    # 测试 生成的数据用于apipost压力测试
    config_path="D:\\Git\\python-samples-hub\\tests\\other_conf\\ok\\server_config_3.ini"
    client_keys = list(server_properties.ServerProperties(config_path, constants.DEFAULT_SCHEDULER_CONFIG).client_info_properties.keys())
    #
    # message_count = 100
    # out_path = 'D:/Temp/stress_test_data.txt'
    # generate_stress_test_data_by_client_id_file(client_keys, message_count, out_path)
    # send_by_client_id_request('xascxadsas5c', '测试消息-1')
    # async_send_by_client_id_file(client_keys, message_count, out_path)
    # thread_send_by_client_id_file(client_keys, message_count, out_path)
    generate_randomized_concurrency_test('************', '7779', client_keys, 3000)
    time.sleep(5)
