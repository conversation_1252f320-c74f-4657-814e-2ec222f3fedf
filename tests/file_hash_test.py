import logging
import os
import sys

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)
logging.basicConfig(
    level=logging.DEBUG,  # 设置日志级别（DEBUG 及以上级别的日志都会被记录）
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',  # 日志格式
    datefmt='%Y-%m-%d %H:%M:%S'  # 时间格式
)
logger = logging.getLogger(__name__)

import hashlib
import tempfile
import pytest
from common.utils.path_utils import file_hash

def test_file_hash_valid_sha256():
    """测试有效的SHA256哈希计算"""
    # 创建临时测试文件
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file.write(b"Hello, world!")
        tmp_file_path = tmp_file.name

    # 计算哈希值
    result = file_hash(tmp_file_path, "sha256")
    expected = hashlib.sha256(b"Hello, world!").hexdigest()

    # 清理
    os.unlink(tmp_file_path)

    assert result == expected

def test_file_hash_valid_md5():
    """测试有效的MD5哈希计算"""
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file.write(b"Test data")
        tmp_file_path = tmp_file.name

    result = file_hash(tmp_file_path, "md5")
    expected = hashlib.md5(b"Test data").hexdigest()

    os.unlink(tmp_file_path)
    assert result == expected

def test_file_hash_empty_file():
    """测试空文件的哈希计算"""
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file_path = tmp_file.name

    result = file_hash(tmp_file_path, "sha256")
    expected = hashlib.sha256(b"").hexdigest()

    os.unlink(tmp_file_path)
    assert result == expected

def test_file_hash_large_file():
    """测试大文件的分块哈希计算"""
    # 创建1MB大小的测试文件
    large_data = b"A" * 1024 * 1024
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file.write(large_data)
        tmp_file_path = tmp_file.name

    result = file_hash(tmp_file_path, "sha256")
    expected = hashlib.sha256(large_data).hexdigest()

    os.unlink(tmp_file_path)
    assert result == expected

def test_file_hash_custom_chunk_size():
    """测试自定义块大小的哈希计算"""
    data = b"Custom chunk size test" * 100
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file.write(data)
        tmp_file_path = tmp_file.name

    # 使用小尺寸块（128字节）
    result = file_hash(tmp_file_path, "sha256", chunk_size=128)
    expected = hashlib.sha256(data).hexdigest()

    os.unlink(tmp_file_path)
    assert result == expected

def test_file_hash_invalid_algorithm():
    """测试不支持的哈希算法"""
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file_path = tmp_file.name

    with pytest.raises(ValueError) as excinfo:
        file_hash(tmp_file_path, "invalid_algorithm")

    os.unlink(tmp_file_path)
    assert "不支持的哈希算法" in str(excinfo.value)

def test_file_hash_nonexistent_file():
    """测试文件不存在的情况"""
    with pytest.raises(ValueError) as excinfo:
        file_hash("/path/to/nonexistent/file.txt", "sha256")

    assert "实际不存在" in str(excinfo.value)

def test_file_hash_negative_chunk_size():
    """测试无效的块大小参数"""
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file_path = tmp_file.name

    with pytest.raises(ValueError) as excinfo:
        file_hash(tmp_file_path, "sha256", chunk_size=-1)

    os.unlink(tmp_file_path)
    assert "greater than 0" in str(excinfo.value)

def test_file_hash_zero_chunk_size():
    """测试零块大小参数"""
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file_path = tmp_file.name

    with pytest.raises(ValueError) as excinfo:
        file_hash(tmp_file_path, "sha256", chunk_size=0)

    os.unlink(tmp_file_path)
    assert "greater than 0" in str(excinfo.value)

def test_file_hash_all_supported_algorithms():
    """测试所有支持的哈希算法"""
    test_data = b"Algorithm test"
    supported_algorithms = [
        "md5", "sha1", "sha224", "sha256", "sha384", "sha512",
        "sha3_224", "sha3_256", "sha3_384", "sha3_512",
        "blake2b", "blake2s"
    ]

    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        tmp_file.write(test_data)
        tmp_file_path = tmp_file.name

    for algo in supported_algorithms:
        result = file_hash(tmp_file_path, algo)
        hasher = hashlib.new(algo)
        hasher.update(test_data)
        expected = hasher.hexdigest()
        assert result == expected

    os.unlink(tmp_file_path)

if __name__ == '__main__':
    pytest.main(["-s", __file__])
