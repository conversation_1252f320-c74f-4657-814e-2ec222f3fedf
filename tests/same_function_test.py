# 判断两个函数是否是同一个函数:包含参数的校验
import logging
import os
import sys
from functools import wraps

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

from common.utils.common_utils import is_same_function

logger = logging.getLogger(__name__)

# 测试用例辅助定义
class TestClass:
    def method(self): pass

    @classmethod
    def class_method(cls): pass

def func(a, b=1): pass


def test_same_function_identical():
    """TC001-相同函数相同参数"""
    assert is_same_function(func, (1,), {}, func, (1,), {}) is True

def test_different_functions():
    """TC001-不同函数相同参数"""
    def func2(a, b=1): pass
    assert is_same_function(func, (1,), {}, func2, (1,), {}) is False

def test_bound_method_same_instance():
    """TC008-相同实例方法"""
    obj = TestClass()
    assert is_same_function(
        obj.method, (), {},
        obj.method, (), {}
    ) is True

def test_bound_method_different_instances():
    """TC002-不同实例方法"""
    obj1 = TestClass()
    obj2 = TestClass()
    assert is_same_function(
        obj1.method, (), {},
        obj2.method, (), {}
    ) is False

def test_positional_vs_keyword_args():
    """TC006-位置参数与关键字参数等价"""
    assert is_same_function(
        func, (1,), {'b': 2},
        func, (), {'a':1, 'b':2}
    ) is True

def test_default_parameter_filling():
    """TC007-默认参数自动填充"""
    assert is_same_function(
        func, (1,), {},
        func, (1,1), {}
    ) is True

def test_decorated_functions():
    """验证装饰器函数与原函数的等价性"""

    # 定义原始函数和装饰器
    def original(x: int) -> int:
        return x * 2

    def decorator(funct):
        @wraps(funct)
        def wrapper(*args, **kwargs):
            return funct(*args, **kwargs)
        return wrapper

    # 创建装饰后的函数
    decorated=decorator(original)

    # 测试用例组
    test_cases = [
        # (func1, args1, kwargs1, func2, args2, kwargs2, 期望结果)
        (decorated, (5,), {}, original, (5,), {}, True),    # 装饰函数 vs 原始函数
        (decorated, (5,), {}, decorated, (5,), {}, True),   # 装饰函数 vs 自身
        (decorated, (5,), {}, decorated, (10,), {}, False)  # 不同参数
    ]

    for case in test_cases:
        result = is_same_function(
            case[0], case[1], case[2],
            case[3], case[4], case[5]
        )
        assert result == case[6], \
            f"""Test failed for:
            Func1: {case[0].__name__ if hasattr(case[0], '__name__') else case[0]} 
            Args1: {case[1]}
            Func2: {case[3].__name__ if hasattr(case[3], '__name__') else case[3]}
            Args2: {case[4]}
            Expected: {case[6]} 
            Actual: {result}"""



def test_variable_arguments_handling():
    """TC004-可变参数处理"""
    def var_func(a, *args, **kwargs): pass
    args1 = (1, 2, 3)
    args2 = (1, (2, 3))
    assert is_same_function(
        var_func, args1, {},
        var_func, args2, {}
    ) is False

def test_invalid_arguments_binding():
    """TC003-参数绑定失败"""
    assert is_same_function(
        func, (1,2,3), {},  # 参数过多
        func, (), {}
    ) is False

if __name__ == '__main__':
    print("测试开始")
