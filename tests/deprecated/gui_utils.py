# 该代码文件存放过期不用的gui_utils的代码
import binascii
import logging
import os
import tkinter
from tkinter import ttk
from typing import Union

from PIL import Image
from config import gui_constants

from common.models import gui_widgets
from common.utils import logging_utils, path_utils

logger = logging.getLogger(__name__)

# 将指定窗口居中
def center_window(cur_gui:Union[tkinter.Tk,tkinter.Toplevel]):
    """
    该方法是将主或者子窗口居于屏幕中间
    废弃原因:其中的主窗口居中已经分离出去,子窗口是居于父窗口中心位置,不再使用该方法
    :param cur_gui: 主窗口或者子窗口
    """
    logging_utils.logger_print(msg="centering window", custom_logger=logger)
    cur_gui.update_idletasks()  # 更新所有挂起的GUI任务
    # 获取初始宽高
    cur_gui_width = cur_gui.winfo_width()
    cur_gui_height = cur_gui.winfo_height()

    # 如果宽高异常（通常是窗口尚未完全渲染），使用默认尺寸并强制设置
    if cur_gui_width <= 1 or cur_gui_height <= 1:
        cur_gui_width = gui_constants.DEFAULT_GUI_WIDTH
        cur_gui_height = gui_constants.DEFAULT_GUI_HEIGHT
        cur_gui.geometry(f"{cur_gui_width}x{cur_gui_height}")
        cur_gui.update_idletasks()

    logging_utils.logger_print(msg=f"window dimensions: {cur_gui_width}x{cur_gui_height}", custom_logger=logger)

    # 获取屏幕尺寸
    screen_width = cur_gui.winfo_screenwidth()
    screen_height = cur_gui.winfo_screenheight()
    logging_utils.logger_print(msg=f"screen dimensions: {screen_width}x{screen_height}", custom_logger=logger)

    x = (screen_width - cur_gui_width )// 2
    y = (screen_height - cur_gui_height )// 2
    logging_utils.logger_print(msg=f"calculated center position: x={x}, y={y}", custom_logger=logger)

    cur_gui.geometry(f"+{x}+{y}")# 设置窗口居中
    logging_utils.logger_print(msg="window geometry set to center position", custom_logger=logger)

    # 确保窗口在屏幕边界内
    cur_gui.update_idletasks()
    if x < 0 or y < 0 or (x + cur_gui_width) > screen_width or (y + cur_gui_height) > screen_height:
        logging_utils.logger_print(msg="window position exceeds screen boundaries, adjusting", custom_logger=logger, log_level=logging.WARNING)
        # 如果窗口超出屏幕边界，重新调整到可见位置
        x = max(0, min(x, screen_width - cur_gui_width))
        y = max(0, min(y, screen_height - cur_gui_height))
        logging_utils.logger_print(msg=f"adjusted position: x={x}, y={y}", custom_logger=logger)
        cur_gui.geometry(f"+{x}+{y}")

    logging_utils.logger_print(msg="window centering completed", custom_logger=logger)

def is_png(file_png_path, strict_check=True)->bool:
    """
    判断文件是否为 PNG 格式
    参数:
        file_path (str): 文件路径
        strict_check (bool): 是否进行完整图像验证（需要PIL库）
    返回:
        bool: True 表示是有效 PNG，False 表示不是

    废弃原因: 该方法的应用场景是判断图片文件是否可用于图标,ttkbootstrap组件中只需要设置主窗口的图标即可一次性满足所有窗口的图标设置需求，不再使用该方法
    """
    real_path = path_utils.get_real_exist_file_path(file_png_path) # 校验文件存在并获取全路径

    # 方法1：完整图像验证（需要安装PIL/Pillow）
    if strict_check:
        try:
            with Image.open(real_path) as img:
                # 验证文件格式和完整性
                img.verify()  # 快速验证数据结构
                return img.format == 'PNG'
        except (IOError, SyntaxError):
            return False

    # 方法2：检查文件头标识（快速可靠）
    try:
        with open(real_path, 'rb') as f:
            # 读取前8字节（PNG文件头特征）
            header = f.read(8)
            # PNG标准文件头: 89 50 4E 47 0D 0A 1A 0A
            return header == b'\x89PNG\r\n\x1a\n'
    except IOError:
        return False

def is_ico(file_ico_path)->bool:
    """
    判断文件是否为 ICO 格式
    :param file_ico_path:
    废弃原因: 该方法的应用场景是判断图片文件是否可用于图标,ttkbootstrap组件中只需要设置主窗口的图标即可一次性满足所有窗口的图标设置需求，不再使用该方法
    """
    real_path = path_utils.get_real_exist_file_path(file_ico_path) # 校验文件存在并获取全路径
    try:
        with open(real_path, 'rb') as f:
            # 读取前6字节（ICO文件头关键标识）
            header = f.read(6)
            if len(header) < 6:
                return False

            # 转换为十六进制便于检查
            hex_header = binascii.hexlify(header).decode('utf-8')

            # 验证文件头结构
            return (
                    hex_header.startswith('0000') and          # 前2字节必须为00 00
                    hex_header[4:8] in ('0100', '0200') and    # 图像类型为ICO或CUR
                    hex_header[8:12] != '0000'                 # 图像数量不能为0
            )
    except (IOError, OSError, binascii.Error):
        return False

# 设置gui界面图标: ico格式图标适合在windows系统中使用，png格式图标适合在其他系统中使用
def set_gui_icon(cur_gui:Union[tkinter.Tk,tkinter.Toplevel], local_img_path:str):
    """
    设置GUI界面图标
    :param cur_gui: 当前窗口对象
    :param local_img_path:  本地图标路径
    废弃原因: 该方法的应用场景是判断图片文件是否可用于图标,ttkbootstrap组件中只需要设置主窗口的图标即可一次性满足所有窗口的图标设置需求，不再使用该方法
    """
    logging_utils.logger_print(msg=f"setting gui icon with path: {local_img_path}", custom_logger=logger)
    real_path = path_utils.get_real_exist_file_path(local_img_path)
    logging_utils.logger_print(msg=f"resolved icon path: {real_path}", custom_logger=logger)

    system_platform = gui_constants.CURRENT_PLATFORM
    logging_utils.logger_print(msg=f"detected system platform: {system_platform}", custom_logger=logger)

    try:
        if system_platform=="win32":
            logging_utils.logger_print(msg="processing icon for windows platform", custom_logger=logger)
            if is_ico(real_path):
                cur_gui.iconbitmap(real_path)
                logging_utils.logger_print(msg="ico icon set successfully", custom_logger=logger)
            else:
                logging_utils.logger_print(msg="icon is not ico format, attempting conversion", custom_logger=logger)
                # 尝试将PNG转为临时ICO
                ico_path = os.path.splitext(real_path)[0] + ".ico"
                logging_utils.logger_print(msg=f"temporary ico path: {ico_path}", custom_logger=logger)
                try:
                    img = Image.open(real_path)
                    logging_utils.logger_print(msg="image opened successfully, converting to ico", custom_logger=logger)
                    img.save(ico_path, format='ICO', sizes=[(256, 256)])
                    logging_utils.logger_print(msg="image converted to ico successfully", custom_logger=logger)
                    cur_gui.iconbitmap(ico_path)
                    logging_utils.logger_print(msg="converted ico icon set successfully", custom_logger=logger)
                    # 程序退出时清理临时文件
                    cur_gui.bind("<Destroy>", lambda event: os.remove(ico_path))
                    logging_utils.logger_print(msg="cleanup handler registered for temporary ico file", custom_logger=logger)
                except Exception: # noqa
                    logging_utils.logger_print(msg="windows icon conversion failed!", custom_logger=logger, use_exception=True)
        else:
            logging_utils.logger_print(msg="processing icon for non-windows platform", custom_logger=logger)
            # macOS/Linux优先使用PNG
            if is_png(real_path):
                logging_utils.logger_print(msg="icon is png format, setting for non-windows platform", custom_logger=logger)
                # 将图标缓存到全局变量防止被回收
                if not hasattr(cur_gui, '_tk_icons'):
                    cur_gui._tk_icons = []
                    logging_utils.logger_print(msg="initialized icon cache for gui", custom_logger=logger)
                icon = tkinter.PhotoImage(file=real_path)
                logging_utils.logger_print(msg="photoimage created from png file", custom_logger=logger)
                cur_gui._tk_icons.append(icon) # noqa
                cur_gui.wm_iconphoto(True, icon)
                logging_utils.logger_print(msg="png icon set successfully", custom_logger=logger)

                # macOS额外处理
                if system_platform == 'darwin':
                    logging_utils.logger_print(msg="applying macos specific icon handling", custom_logger=logger)
                    cur_gui.tkinter.call('wm', 'iconphoto', cur_gui._w, icon)  # noqa
                    logging_utils.logger_print(msg="macos icon handling completed", custom_logger=logger)
            else:
                logging_utils.logger_print(msg="icon is not png format, attempting ico fallback", custom_logger=logger)
                # 尝试非Windows平台使用ICO
                try:
                    cur_gui.iconbitmap(real_path)
                    logging_utils.logger_print(msg="ico icon set successfully on non-windows platform", custom_logger=logger)
                except:  # noqa
                    logging_utils.logger_print(msg="non-windows platform recommends png format icons", custom_logger=logger, log_level=logging.WARNING)
    except Exception: # noqa
        logging_utils.logger_print(msg=f"icon setting failed: {local_img_path}!", custom_logger=logger, use_exception=True)

def common_gui_do(cur_gui:Union[tkinter.Tk,tkinter.Toplevel],local_icon_path:str):
    """
    通用GUI设置:窗口居中、设置图标
    废弃原因: ttkbootstrap组件中只需要设置主窗口的图标即可一次性满足所有窗口的图标设置需求，不再需要重复设置图标;其居中方法也区分了主窗口和子窗口，不再使用center_window方法
    :param cur_gui:
    :param local_icon_path:
    :return:
    """
    logging_utils.logger_print(msg=f"applying common gui settings to window: {cur_gui}", custom_logger=logger)
    logging_utils.logger_print(msg="centering window", custom_logger=logger)
    center_window(cur_gui)
    logging_utils.logger_print(msg="setting gui icon", custom_logger=logger)
    set_gui_icon(cur_gui, local_icon_path)
    logging_utils.logger_print(msg="common gui settings applied successfully", custom_logger=logger)

def create_notification_bar(win: tkinter.Tk, text: str):
    """
    创建主界面上置顶通知栏
    废弃原因: 当前使用ttkbootstrap组件,新方法在ttkb_gui_utils.py中实现
    """
    notification_container = ttk.Frame(win)
    notification_container.pack(side="top", fill="x")
    # 禁止自动缩放，这样即便子控件被隐藏，容器高度也不会改变
    notification_container.pack_propagate(False)
    # 固定容器高度，与 Marquee 一致
    notification_container.configure(height=18 + 8)  # font size(10) + padding(8)

    # 在容器中放入通知栏
    notif = gui_widgets.NotificationBar(parent=notification_container, text=text, font_family_size=("Arial", 10))
    notif.pack(fill="both", expand=True)
