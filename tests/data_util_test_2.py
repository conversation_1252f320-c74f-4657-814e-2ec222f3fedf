import logging
import os
import shutil
import sqlite3
import sys
import tempfile
import time
import unittest
from datetime import datetime, timedelta
from unittest.mock import patch
from zoneinfo import ZoneInfo

import ulid
from config import gui_constants

from common.models.sqlite_base_manager import SQLiteBaseManager

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

from common.utils import self_log
from config import  constants
from models.server_data_manager import WebhookDataManager

self_log.setup_logging("../resources/log.ini")
logger = logging.getLogger(__name__)


class TestDataManager(unittest.TestCase):
    def setUp(self):
        self.db_fd, self.db_path = tempfile.mkstemp()
        self.zone = ZoneInfo("Asia/Shanghai")
        self.manager = WebhookDataManager(self.db_path, constants.DEFAULT_TIMEZONE)

    def tearDown(self):
        os.close(self.db_fd)
        os.unlink(self.db_path)
        shutil.rmtree('_trial_temp', ignore_errors=True)

    # 测试初始化逻辑
    def test_initialization(self):
        # 验证数据库文件创建
        self.assertTrue(os.path.exists(self.db_path))
        logger.info(f"db_path: {self.db_path},zone:{self.zone}")
        logger.info(f"data manager db_path: {self.manager.db_path},data manager zone:{self.manager.zone}")
        self.manager.check_wal_size()
        self.manager.check_integrity()
        # 验证表结构
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(messages)")
            columns = [row[1] for row in cursor.fetchall()]
            self.assertListEqual(columns, ['id', 'message', 'client_key', 'reception_time', 'is_read'])

    # 测试事务执行和重试机制
    @patch('sqlite3.connect')
    def test_exec_retry(self, mock_connect):
        mock_connect.side_effect = sqlite3.OperationalError("database is locked")
        with self.assertRaises(sqlite3.OperationalError):
            self.manager._exec_retry(lambda c: c.execute("SELECT 1"))

    # 测试消息保存功能
    def test_save_message(self):
        # 正常保存
        test_message = "a" * constants.MIN_MESSAGE_LEN
        test_key = "k" * constants.MIN_CLIENT_KEY_LEN
        self.manager.save_message(test_message, test_key)

        # 验证数据插入
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM messages")
            self.assertEqual(cursor.fetchone()[0], 1)

        # 测试边界值
        with self.assertRaises(ValueError):
            self.manager.save_message("", test_key)
        with self.assertRaises(ValueError):
            self.manager.save_message("a" * (constants.MAX_MESSAGE_LEN + 1), test_key)
        with self.assertRaises(ValueError):
            self.manager.save_message(test_message, "")
        with self.assertRaises(ValueError):
            self.manager.save_message(test_message, "k" * (constants.MAX_CLIENT_KEY_LEN + 1))

    # 测试时区转换
    def test_convert_utc_str(self):
        utc_str = datetime.now(constants.UTC_ZONE).strftime(gui_constants.DATETIME_FORMAT)
        local_str = SQLiteBaseManager.convert_utc_str(utc_str, self.zone)
        self.assertNotEqual(utc_str, local_str)
        self.assertEqual(datetime.now(self.zone).strftime(gui_constants.DATETIME_FORMAT), local_str)
        logger.info(f"utc_str: {utc_str},local_str: {local_str}")

    # 新增测试数据
    def add_test_data(self):
        # 准备测试数据
        test_data = [
            ("msg1", "unique_client_key_1", datetime.now(constants.UTC_ZONE) - timedelta(minutes=10)),
            ("msg2", "unique_client_key_2", datetime.now(constants.UTC_ZONE) - timedelta(minutes=5)),
            ("msg3", "unique_client_key_3", datetime.now(constants.UTC_ZONE))
        ]

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            for msg, key, dt in test_data:
                cursor.execute(
                    "INSERT INTO messages (id, message, client_key, reception_time) VALUES (?,?,?,?)",
                    (ulid.new().str, msg, key, dt.strftime(gui_constants.DATETIME_FORMAT))
                )

    # 测试消息读取功能
    def test_read_oldest_data(self):
        self.add_test_data()
        # 测试获取最旧消息:未读数据使用一次之后就会变成已读数据
        oldest = self.manager.get_oldest_unread(2, "unique_client_key_1")
        self.assertEqual(len(oldest), 1)
        self.assertEqual(oldest[0].message, "msg1")
        oldest = self.manager.get_oldest_unread(2, "unique_client_key_unknown")
        self.assertEqual(len(oldest), 0)
        # 三条数据,其中一条已经被读取了
        oldest = self.manager.get_oldest_unread(5)
        self.assertEqual(len(oldest), 2)
        self.assertEqual(oldest[0].message, "msg2")
        # 边界值测试
        with self.assertRaises(ValueError):
            self.manager.get_oldest_unread(constants.MIN_UNREAD_MSGS_LEN - 2)
        with self.assertRaises(ValueError):
            self.manager.get_oldest_unread(constants.MAX_UNREAD_MSGS_LEN + 2)

    # c
    def test_read_recent_data(self):
        self.add_test_data()
        # 测试获取最新消息
        # 1.
        recent = self.manager.get_recent_unread(2, 11, "unique_client_key_unknown")
        self.assertEqual(len(recent), 0)
        # 2.
        recent = self.manager.get_recent_unread(2, 9, "unique_client_key_1")
        self.assertEqual(len(recent), 0)
        # 3.
        recent = self.manager.get_recent_unread(2, 11, "unique_client_key_1")
        self.assertEqual(len(recent), 1)
        self.assertEqual(recent[0].message, "msg1")
        # 4.
        recent = self.manager.get_recent_unread(2, 1)
        self.assertEqual(len(recent), 1)
        self.assertEqual(recent[0].message, "msg3")
        # 5. 就剩一条未读消息
        recent = self.manager.get_recent_unread(2, 10)
        self.assertEqual(len(recent), 1)
        self.assertEqual(recent[0].message, "msg2")

        # 边界值测试
        with self.assertRaises(ValueError):
            self.manager.get_recent_unread(constants.MIN_UNREAD_MSGS_LEN - 2, 1)
        with self.assertRaises(ValueError):
            self.manager.get_recent_unread(constants.MAX_UNREAD_MSGS_LEN + 2, 1)
        with self.assertRaises(ValueError):
            self.manager.get_recent_unread(1, constants.EARLIEST_RECENTLY_UNREAD_TIME - 2)
        with self.assertRaises(ValueError):
            self.manager.get_recent_unread(1, constants.LATEST_RECENTLY_UNREAD_TIME + 2)

    def test_invalid_days_negative(self):
        with self.assertRaises(ValueError) as context:
            self.manager.get_older_unread_count(0)
        self.assertEqual(str(context.exception), "the specified days must be greater than 0")

    def test_valid_day_count(self):
        # Mock数据库返回10条记录
        with patch.object(WebhookDataManager, '_exec_retry') as mock_retry:
            mock_retry.return_value = 10
            result = self.manager.get_older_unread_count(5)
            self.assertEqual(result, 10)

    def test_sql_injection_protection(self):
        # 测试SQL注入防护
        with patch.object(WebhookDataManager, '_exec_retry') as mock_retry:
            self.manager.get_older_unread_count(5)
            mock_retry.assert_called_once()
            args, _ = mock_retry.call_args
            closure_contents = [c.cell_contents for c in args[0].__closure__]
            self.assertEqual(closure_contents[0], 5)  # 验证参数化查询

    def test_query_structure(self):
        # 验证SQL语句结构
        expected_sql = """
            SELECT COUNT(*) 
            FROM messages 
            WHERE is_read = 0 
            AND reception_time <= datetime('now', '-' || ? || ' days');"""
        with patch.object(WebhookDataManager, '_exec_retry') as mock_retry:
            self.manager.get_older_unread_count(3)
            closure_contents = [c.cell_contents for c in mock_retry.call_args[0][0].__closure__]
            executed_sql = closure_contents[1]
            self.assertEqual(executed_sql.strip(), expected_sql.strip())

    def test_database_error_handling(self):
        # 模拟数据库错误
        with patch.object(WebhookDataManager, '_exec_retry',
                          side_effect=sqlite3.OperationalError("Database locked")):
            with self.assertRaises(sqlite3.OperationalError):
                self.manager.get_older_unread_count(1)

    def test_boundary_condition_max_days(self):
        # 测试边界条件（极大天数）
        with patch.object(WebhookDataManager, '_exec_retry') as mock_retry:
            mock_retry.return_value = 5
            result = self.manager.get_older_unread_count(365)
            self.assertEqual(result, 5)

    def test_zero_result_handling(self):
        # 测试无数据返回情况
        with patch.object(WebhookDataManager, '_exec_retry') as mock_retry:
            mock_retry.return_value = 0
            result = self.manager.get_older_unread_count(7)
            self.assertEqual(result, 0)

    def add_get_older_unread_count_test_data(self):
        # 准备多维度测试数据（覆盖不同时间、阅读状态）
        now = datetime.now(constants.UTC_ZONE)
        test_data = [
            # (消息内容, 客户端密钥, 接收时间, 是否已读)
            ("3day_unread", "unique_client_key_1_A", now - timedelta(days=3), 0),  # 应被计数
            ("5day_unread", "unique_client_key_1_B", now - timedelta(days=5), 0),  # 应被计数
            ("2day_unread", "unique_client_key_1_C", now - timedelta(days=2), 0),  # 不应被计数
            ("7day_read", "unique_client_key_1_D", now - timedelta(days=7), 1),  # 不应被计数
            ("4day_unread", "unique_client_key_1_A", now - timedelta(days=4), 0)  # 应被计数
        ]

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            for msg, key, dt, read_status in test_data:
                cursor.execute(
                    "INSERT INTO messages (id, message, client_key, is_read, reception_time) VALUES (?,?,?,?,?)",
                    (ulid.new().str, msg, key, read_status, dt.strftime(gui_constants.DATETIME_FORMAT))
                )
            conn.commit()

    # 新增的测试用例
    def test_realdata_count_logic(self):
        self.add_get_older_unread_count_test_data()

        # 测试3天阈值
        self.assertEqual(self.manager.get_older_unread_count(3), 3)  # 3,5,4天未读

        # 测试5天阈值
        self.assertEqual(self.manager.get_older_unread_count(5), 1)  # 仅5天未读

        # 测试客户端过滤
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("UPDATE messages SET is_read=1 WHERE message='5day_unread'")
        self.assertEqual(self.manager.get_older_unread_count(3), 2)

    def test_edge_case_data(self):
        # 边界时间测试数据
        now = datetime.now(constants.UTC_ZONE)
        edge_time = now - timedelta(days=2, minutes=1)  # 刚好超过2天
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "INSERT INTO messages (id, message, client_key, is_read, reception_time) VALUES (?,?,?,?,?)",
                (ulid.new().str, "edge_case", "unique_client_key_X", 0, edge_time.strftime(gui_constants.DATETIME_FORMAT))
            )

        # 测试2天边界
        self.assertEqual(self.manager.get_older_unread_count(2), 1)  # 应包含edge_case
        self.assertEqual(self.manager.get_older_unread_count(3), 0)  # 不应包含

    def test_mixed_timezones(self):
        # 时区敏感测试
        shanghai_time = datetime.now(ZoneInfo("Asia/Shanghai"))
        with sqlite3.connect(self.db_path) as conn:
            # 插入带时区的时间（但数据库存储的是UTC）
            conn.execute(
                "INSERT INTO messages (id, message, client_key, is_read, reception_time) VALUES (?,?,?,?,?)",
                (ulid.new().str, "tz_test", "unique_client_key_Z", 0,
                 shanghai_time.astimezone(constants.UTC_ZONE).strftime(gui_constants.DATETIME_FORMAT))
            )

        # 验证时区转换不影响计数逻辑
        self.assertEqual(self.manager.get_older_unread_count(1), 0)  # 当前时间不应被计数

    # 测试数据清理功能
    def test_data_cleanup(self):
        # 插入已读测试数据
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            for i in range(10):
                logger.info(f"cur loop-{i}")
                cursor.execute(
                    "INSERT INTO messages (id, message, client_key, is_read) VALUES (?,?,?,1)",
                    (ulid.new().str, f"msg{i}", "unique_client_key_1")
                )

        # 测试过期数据删除
        self.manager.remove_expired_read_data(-1)  # 应跳过删除
        self.manager.remove_expired_read_data(1)

        # 测试超额数据删除[当前测试数据无法超过constants.MAX_MESSAGE_COUNT条]
        self.manager.remove_excess_read_data(constants.MAX_MESSAGE_COUNT + 1)
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM messages")
            self.assertLessEqual(cursor.fetchone()[0], 10)

    # 测试数据库维护功能
    def test_maintenance(self):
        # 测试WAL检查
        with patch.object(self.manager, 'check_wal_size') as mock_wal:
            self.manager._wal_loop()
            time.sleep(1)
            self.assertGreaterEqual(mock_wal.call_count, 0)

        # 测试完整性检查
        with self.assertRaises(sqlite3.DatabaseError):
            with open(self.db_path, 'wb') as f:
                f.write(b'corrupted data')
            self.manager.check_integrity()

    # 完整流程测试
    def test_full_workflow(self):
        # 1. 保存消息
        self.manager.save_message("流程测试消息", "workflow_key")

        # 2. 读取消息
        messages = self.manager.get_oldest_unread(1)
        self.assertEqual(len(messages), 1)
        logger.info(f"oldest message: {messages[0]}")
        self.assertEqual(messages[0].message, "流程测试消息")

        # 3. 验证已读标记
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT is_read FROM messages")
            self.assertEqual(cursor.fetchone()[0], 1)

        # 4. 清理数据
        self.manager.remove_expired_read_data(1)
        self.manager.remove_excess_read_data(constants.MAX_MESSAGE_COUNT + 1)

        # 5. 维护操作
        self.manager.check_wal_size()
        self.manager.check_integrity()


if __name__ == '__main__':
    unittest.main()
