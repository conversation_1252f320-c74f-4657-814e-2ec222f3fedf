import ctypes
import logging
from pathlib import Path
from tkinter import font

import pyglet
import ttkbootstrap as ttkb
from config import gui_constants
from fontTools.ttLib import TTFont
from ttkbootstrap import Toplevel
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import MessageDialog
from ttkbootstrap.icons import Icon

from common.utils import gui_utils, ttkb_gui_utils
from deprecated.gui_widgets import CollapsingFrame

font_path="D://Git//python-samples-hub//resources//NotoSansSC-VariableFont_wght.ttf"
logger=logging.getLogger(__name__)
def font_file_test(path):
    root = ttkb.Window()
    root.withdraw()  # 不显示窗口
    try:
        root.tk.eval(f"font create testfont -file {{{path}}}")
        print("✅ 字体文件有效")
        root.tk.eval("font delete testfont")
    except Exception as e:
        print(f"❌ 错误: {e}")
    root.destroy()


def load_font_temporary(tmp_font_path):
    """临时加载字体（仅当前进程有效）"""
    try:
        # 加载字体
        fr_private = 0x10
        if ctypes.windll.gdi32.AddFontResourceExW(ctypes.c_wchar_p(tmp_font_path),
                                                  fr_private, 0) == 0:
            return False

        # 通知系统刷新
        ctypes.windll.user32.SendMessageW(0xFFFF, 0x001D, 0, 0)  # WM_FONTCHANGE
        return True
    except:  # noqa
        return False

def load_tmp_font_test():
    """临时加载字体测试"""
    if load_font_temporary(font_path):
        root = ttkb.Window(themename='flatly')
        root.geometry("700x600")
        body = ttkb.Label(root, text="主界面内容在这里")
        body.pack(expand=True)
        print(f"字体属性:{font.Font(font=body['font']).actual()}")
        root.mainloop()
    else:
        print("加载字体失败")


def get_font_name_from_file(path):
    name = ""
    for record in TTFont(path)['name'].names:
        if record.nameID == 1 and record.platformID == 3:  # NameID 1: Font Family
            name = record.string.decode('utf-16-be')
            break
    return name

def pyglet_font_test():
    """pyglet字体加载测试"""
    pyglet.options['win32_gdi_font'] = True

    pyglet.font.add_file(font_path)
    root = ttkb.Window(themename='flatly')
    custom_font=font.Font(family=get_font_name_from_file(font_path), size=11)
    root.option_add('*Font',custom_font)
    root.geometry("700x600")
    body = ttkb.Label(root, text="主界面内容在这里")
    body.pack(expand=True)
    print(f"字体属性:{font.Font(font=body['font']).actual()}")

# --------------------------------- 有效测试 --------------------------------------------
def show_notification_test(main_win:ttkb.Window):
    """显示通知栏测试"""
    notification_bar_font=("TkDefaultFont", 10)
    text="每个人都不得不面对这些问题。 在面对这种问题时， 一般来讲，我们都必须务必慎重的考虑考虑。 就我个人来说，代码对我的意义，不能不说非常重大。 罗曼·罗兰曾经说过，只有把抱怨环境的心情，化为上进的力量，才是成功的保证。这启发了我， 我们一般认为，抓住了问题的关键，其他一切则会迎刃而解。 吉格·金克拉曾经说过，如果你能做梦，你就能实现它。我希望诸位也能好好地体会这句话。 我们一般认为，抓住了问题的关键，其他一切则会迎刃而解。 所谓代码，关键是代码需要如何写"
    ttkb_gui_utils.show_notification(main_win, text, notification_bar_font)

def show_all_fonts():
    root = ttkb.Window()
    root.withdraw()  # 不显示主窗口

    # 获取字体列表
    available_fonts = sorted(font.families())

    # 打印前几十个字体
    for f in available_fonts[:50]:
        print(f)

    print(f"\n共找到 {len(available_fonts)} 个字体。")

def first_available_font_test():
    """获取第一个可用字体测试"""
    root = ttkb.Window(themename='flatly')
    ttkb_gui_utils.set_available_unified_font_families(root)
    root.geometry("700x600")
    body = ttkb.Label(root, text="主界面内容在这里")
    body.pack(expand=True)
    # 自定义按钮字体依旧生效
    ttkb.Style().configure("My.TButton", font=("Arial", 14))
    ttkb.Button(root, text="测试按钮",style="My.TButton").pack(pady=10)
    print(f"字体属性:{font.Font(font=body['font']).actual()}")
    root.mainloop()




# 主窗口启动时锁需要的参数:只需要在主窗口设置一次图标即可
window_config_dict={'title':'测试主窗口','themename':'flatly','size':(700, 600),'resizable':(True, True),'alpha':0.99,'iconphoto':gui_constants.SMALL_ICON_PATH}

def get_main_win()->ttkb.Window:
    """获取主窗口"""
    root = ttkb.Window(**window_config_dict)
    ttkb_gui_utils.set_available_unified_font_families(root)
    gui_utils.center_window_on_screen(root)
    return root

def gui_cent_test():
    """GUI居中测试"""
    root = get_main_win()
    # try:
    #     large_icon=tkinter.PhotoImage(file=gui_constants.LARGE_ICON_PATH, master=root)
    #     small_icon=tkinter.PhotoImage(file=gui_constants.SMALL_ICON_PATH, master=root)
    #     root._icon = large_icon
    #     root.iconphoto(True, large_icon)
    # except BaseException:
    #     from common.utils.logging_utils import logger_print
    #     logger_print(msg="设置图标失败", custom_logger=logger, log_level=logging.ERROR, use_exception=True)
    body = ttkb.Label(root, text="主界面内容在这里")
    body.pack(expand=True)
    # root.place_window_center()
    # dialog = MessageDialog(title="提示", message="窗口已居中", buttons=["确认:danger"],icon=Icon.error, parent=root,alert=True)
    # dialog.show()
    # ttkb_gui_utils.dialog_center_in_parent(root, dialog)
    # ttkb_gui_utils.dialog_center_in_parent(root, MessageDialog(title="xxx提示消息", message="给gui界面查看的提示消息", buttons=["确认:dark"], parent=root,alert=True))
    ttkb_gui_utils.dialog_center_in_parent(root, MessageDialog(title="xxx错误", message="给gui界面查看的错误消息", buttons=["确认:danger"], icon=Icon.error, parent=root, alert=True))
    # dialog.update_idletasks()
    # print(f"messagebox:width={dialog.winfo_width()}, height={dialog.winfo_height()}")
    # Messagebox.show_info(title="提示", message="窗口已居中",parent=root,position=(0, 300))
    root.mainloop()


def top_level_test():
    """顶级子窗口测试"""
    root = get_main_win()
    def open_toolwindow():
        win = Toplevel(title="工具窗口", master=root,topmost=True,toolwindow=False,size=(300, 200),resizable=(False, False))
        ttkb_gui_utils.comm_child_win_do(win, root)
        ttkb.Label(win, text="工具窗口内容在这里").pack(expand=True)

    ttkb.Button(root, text="打开工具窗口", command=open_toolwindow).pack(pady=20)
    root.mainloop()


def meter_show_test():
    """仪表盘测试"""
    root = get_main_win()
    cpu_meter = ttkb_gui_utils.create_cpu_meter(root, 150, 8)
    cpu_meter.pack(side=TOP,anchor=NW,padx=10,pady=10)
    # 模拟更新函数
    def simulate_update():
        import random
        value = random.uniform(0, 100)

        ttkb_gui_utils.update_cpu_meter(cpu_meter, value)
        root.after(1000, simulate_update)  # noqa 每秒更新一次

    root.after(1000, simulate_update)  # noqa 启动模拟更新

    root.mainloop()

def meter_int_same_float_diff_show_test():
    """仪表盘小数点不同显示测试"""
    root = get_main_win()
    cpu_meter_1 = ttkb_gui_utils.create_cpu_meter(root, 150, 8)
    cpu_meter_1.pack()
    ttkb_gui_utils.update_cpu_meter(cpu_meter_1, 13.1)
    cpu_meter_2 = ttkb_gui_utils.create_cpu_meter(root, 150, 8)
    cpu_meter_2.pack()
    ttkb_gui_utils.update_cpu_meter(cpu_meter_2, 13.9)
    root.mainloop()
IMG_PATH = Path(__file__).parent.parent / 'resources'
def collapsing_frame_show_test():
    """折叠面板测试"""
    shrinking_path=str(IMG_PATH/'icons8_double_up_24px.png')
    expanding_path=str(IMG_PATH/'icons8_double_right_24px.png')
    app = get_main_win()

    # bottom panel
    bottom_panel = ttkb.Frame(app, style='bg.TFrame')
    bottom_panel.pack(side=BOTTOM,fill=X) #
    collapsing = CollapsingFrame(bottom_panel, shrinking_path=shrinking_path, expanding_path=expanding_path)
    collapsing.pack(fill=X, pady=1)
    ## container
    bus_frm = ttkb.Frame(collapsing, padding=5)
    bus_frm.columnconfigure(1, weight=1)
    collapsing.add(
        child=bus_frm,
        title='Backup Summary',
        bootstyle=SECONDARY)

    ## destination
    lbl = ttkb.Label(bus_frm, text='Destination:')
    lbl.grid(row=0, column=0, sticky=W, pady=2)
    lbl = ttkb.Label(bus_frm, text='destination')
    lbl.grid(row=0, column=1, sticky=EW, padx=5, pady=2)
    app.mainloop()

def treeview_no_tree_show_0_test():
    """treeview中不显示tree但传入#0测试"""
    app = get_main_win()
    frame = ttkb.LabelFrame(app, text="测试框")
    frame.pack(fill=X, expand=False, padx=10, pady=(0, 10))

    columns = ("#0", "name", "last_used", "info")
    cur_treeview = ttkb.Treeview(frame, columns=columns, show="tree headings", selectmode="browse",height=gui_constants.CONFIG_PAGE_NUM)
    cur_treeview.column("#0", width=80,stretch=False, anchor=CENTER)
    cur_treeview.column("name", width=200,stretch=False, anchor=CENTER)  # 增加配置名称列宽度
    cur_treeview.column("last_used", width=120,stretch=False, anchor=CENTER)
    cur_treeview.column("info", width=250,stretch=False, anchor=CENTER)  # 增加附加信息列宽度
    
    # 设置表头
    cur_treeview.heading("#0", text="状态")
    cur_treeview.heading("name", text="配置名称")
    cur_treeview.heading("last_used", text="最后使用")
    cur_treeview.heading("info", text="附加信息")
    cur_treeview.pack(fill=X, expand=False, padx=10, pady=10)

    app.mainloop()

if __name__ == "__main__":
    # first_available_font_test()
    # MessageDialog(title="xxx错误", message="给gui界面查看的错误消息", buttons=["确认:danger"],icon=Icon.error, alert=True).show()
    # messagebox.showerror("程序崩溃", f"发生未预期错误: xxx!\n\n请将日志文件发送给开发者处理")
    # messagebox.showinfo(message= "无效配置不存在")
    # gui_cent_test()
    # top_level_test()
    # meter_show_test()
    # meter_int_same_float_diff_show_test()
    collapsing_frame_show_test()
    # print(Style().theme_names())
    # treeview_no_tree_show_0_test()
