#!/usr/bin/env python3
"""
自动转换项目中的导入语句，将from...import语句替换为import语句
"""

import os
import re
import ast
from typing import Dict, List, Set, Tuple
from pathlib import Path

def should_convert_import(module_name: str) -> bool:
    """判断是否应该转换这个导入语句"""
    internal_modules = [
        'common.utils',
        'common.models', 
        'common.constants',
        'webhook_server.utils',
        'webhook_server.models',
        'webhook_server.config'
    ]
    
    for internal_mod in internal_modules:
        if module_name.startswith(internal_mod):
            return True
    return False

def analyze_file_imports(file_path: str) -> Dict:
    """分析文件中的导入语句"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    imports_to_convert = []
    
    try:
        tree = ast.parse(content)
        for node in ast.walk(tree):
            if isinstance(node, ast.ImportFrom) and node.module:
                if should_convert_import(node.module):
                    items = [alias.name for alias in node.names]
                    imports_to_convert.append({
                        'line_num': node.lineno,
                        'module': node.module,
                        'items': items,
                        'original_line': lines[node.lineno - 1].strip()
                    })
    except Exception as e:
        print(f"解析文件 {file_path} 时出错: {e}")
        return {'content': content, 'imports': []}
    
    return {'content': content, 'imports': imports_to_convert}

def convert_file_imports(file_path: str) -> bool:
    """转换单个文件的导入语句"""
    print(f"处理文件: {file_path}")

    file_info = analyze_file_imports(file_path)
    if not file_info['imports']:
        print(f"  无需转换的导入语句")
        return True

    content = file_info['content']
    lines = content.split('\n')

    # 收集需要添加的模块导入和替换映射
    modules_to_import = set()
    item_to_module = {}  # 记录每个导入项对应的模块

    # 按行号倒序处理，避免行号变化影响
    imports_by_line = sorted(file_info['imports'], key=lambda x: x['line_num'], reverse=True)

    for import_info in imports_by_line:
        line_num = import_info['line_num']
        module = import_info['module']
        items = import_info['items']

        print(f"  第{line_num}行: from {module} import {', '.join(items)}")

        # 替换原来的from import语句为模块导入
        # 例如: from common.utils.network_utils import is_port_occupied, is_ip_in_whitelist
        # 替换为: from common.utils import network_utils
        parent_module = module.rsplit('.', 1)[0]  # common.utils
        module_name = module.rsplit('.', 1)[1]    # network_utils

        new_import_line = f"from {parent_module} import {module_name}"
        lines[line_num - 1] = new_import_line

        # 记录模块导入
        modules_to_import.add(module)

        # 记录每个导入项的模块映射
        for item in items:
            item_to_module[item] = module_name

    # 更新代码中的函数调用
    for i, line in enumerate(lines):
        for item, module_name in item_to_module.items():
            # 替换函数调用，添加模块前缀
            # 使用正则表达式确保只替换独立的标识符
            pattern = r'\b' + re.escape(item) + r'\b'
            # 检查是否已经有模块前缀，避免重复替换
            if f'{module_name}.{item}' not in line and item in line:
                lines[i] = re.sub(pattern, f'{module_name}.{item}', line)

    # 写回文件
    new_content = '\n'.join(lines)

    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"  ✓ 转换完成")
        return True
    except Exception as e:
        print(f"  ✗ 写入文件失败: {e}")
        return False

def main():
    """主函数"""
    src_dir = 'src'
    
    # 获取所有Python文件
    python_files = []
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    print(f"找到 {len(python_files)} 个Python文件")
    print("=" * 80)
    
    success_count = 0
    total_count = len(python_files)
    
    for file_path in python_files:
        if convert_file_imports(file_path):
            success_count += 1
    
    print("=" * 80)
    print(f"转换完成: {success_count}/{total_count} 个文件成功处理")

if __name__ == '__main__':
    main()
