# 多类别设备数据接收存储服务端

> 本项目是一个基于FastAPI和tkinter的多类别设备数据接收存储服务端，支持局域网内多种设备的实时数据接收、存储和分发。

## 项目简介

该软件可以用于接收并存储多种类型设备的实时数据并分发给指定的第三方使用。主要功能包括：

- **数据接收**：通过HTTP API接收来自局域网内各种设备的数据
- **身份验证**：支持基于客户端标识的设备认证和API密钥验证
- **数据存储**：使用SQLite数据库存储接收到的数据，支持自动清理过期数据
- **实时监控**：提供图形界面实时显示接收到的数据和系统状态
- **多进程支持**：支持命令行和GUI两种运行模式，可同时运行多个实例
- **配置管理**：支持多配置文件管理，防止配置冲突

## 项目结构

```
src/
├── webhook_server.config/                 # 配置相关模块
│   ├── constants.py        # 项目常量定义
│   ├── gui_constants.py    # GUI界面常量
│   └── config_check.py     # 配置验证模块
├── webhook_server.models/                 # 核心业务模型
│   ├── webhook_server.py   # Webhook服务器核心类
│   ├── server_data.py      # 数据模型定义
│   ├── server_properties.py # 服务器属性管理
│   └── ...                 # 其他业务模型
├── webhook_server.utils/                  # 工具函数模块
│   ├── server_utils.py     # 服务器工具函数
│   ├── self_log.py         # 日志管理
│   └── ...                 # 其他工具模块
├── webhook_server_gui.py   # GUI主界面
├── webhook_server_command.py # 命令行启动模块
└── config_selection_gui.py # 配置选择界面
```

## 运行方式

### 1. 配置选择界面启动
```bash
python <目录路径>/config_selection_gui.py
```

### 2. GUI界面启动
```bash
python <目录路径>/webhook_server_gui.py --webhook_server.config <配置文件路径>
# 或者
python <目录路径>/config_selection_gui.py    --main-gui <配置文件路径>
```

### 3. 命令行启动
```bash
python <目录路径>/webhook_server_command.py --webhook_server.config <配置文件路径>
```

### 4. 其他
```cmd
# 非服务端端Windows系统下cmd命令行进行操作
curl -X POST ^
  "http://ip:port/webhook/save" ^
  -H "Connection: keep-alive" ^
  -H "Content-Type: application/json" ^
  -H "X-Client-Key: 1234567890" ^
  -d "{\"content\":\"----------------第1次-----------------------\"}"

curl -X GET ^
  "http://ip:port/webhook/token" ^
  -H "Connection: keep-alive" ^
  -H "Authorization: Bearer abcdefghijklmn123456789"

curl -X GET ^
  "http://ip:port/webhook/unread?client_key=1234567890&minutes=1000&size=10" ^
  -H "Connection: keep-alive"  ^
  -H "Authorization: Bearer 上一步骤获取到的token"
  
# exe直接进入主界面
NexusRecv.exe   --main-gui C:\Users\<USER>\.webhook_server\server_config_4.ini
```

## 依赖安装

```bash
pip install -r requirements.txt
```

## API接口说明

### 1. 获取访问令牌
- **接口**: `GET /webhook/token`
- **认证**: Bearer Token (使用配置文件中的api_key)
- **功能**: 获取用于访问其他API的临时令牌

### 2. 保存数据
- **接口**: `POST /webhook/save`
- **请求头**: `X-Client-Key: <设备标识>`
- **请求体**: `{"content": "数据内容"}`
- **功能**: 接收并存储来自指定设备的数据

### 3. 获取未读数据
- **接口**: `GET /webhook/unread?size=<数量>&client_key=<设备标识>`
- **认证**: Bearer Token (通过/webhook/token获取)
- **功能**: 获取指定设备或所有设备的未读数据

## 配置文件说明

项目使用INI格式的配置文件，主要包含以下配置项：

### [server] 节点配置项
- `api_key`: API访问密钥，用于获取访问令牌
- `whitelist`: IP白名单，支持单个IP或网段，使用*表示允许所有IP
- `host`: 服务器监听地址，建议使用0.0.0.0
- `port`: 服务器监听端口
- `message_data_table_name`: 数据库表名
- `log_config_path`: 日志配置文件路径
- `run_time`: 服务器运行时间段，格式为HH:MM-HH:MM
- `time_zone`: 时区设置，如Asia/Shanghai
- `expire_data_days`: 数据过期保存天数
- `data_limit_num`: 数据存储上限数量
- `app_name`: 应用名称
- `enable_sql_logging`: 是否启用SQL日志记录

### [client_info] 节点配置项
存储发信设备的标识信息，格式为：
```ini
[client_info]
设备标识1 = 设备描述1
设备标识2 = 设备描述2
```

## 使用注意事项

### 开发人员注意事项

1. **环境要求**
   - Python 3.11+
   - 支持Windows、Linux、macOS系统
   - 需要安装requirements.txt中的所有依赖

2. **代码结构**
   - 使用pytest进行单元测试，不使用unittest
   - 遵循单例模式，防止多实例冲突
   - 支持多进程运行，使用SQLite进行进程间配置同步
   - 在代码中的导入:在包含sys.path.append(src_path)的类中不能使用 'from . import xxx'

3. **测试环境搭建**
```python
import logging
import sys

src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent) # noqa
if src_path not in sys.path:
    sys.path.append(src_path)
   
logger = logging.getLogger(__name__)
```

4. **配置文件管理**
   - 配置文件必须使用UTF-8编码
   - 支持多配置文件并发使用，但同一配置文件不能被多个实例同时使用
   - 配置项具有严格的格式验证，详见constants.py中的正则表达式

5. **日志系统**
   - 支持按日期和级别分割日志文件
   - 日志文件自动清理，可配置保留天数
   - 支持彩色控制台输出

6. **exe打包**
   - 使用pyinstaller进行exe打包,当前项目不能使用onefile模式,原因在于onefile模式执行使用会在临时目录中生成工作目录,每一次使用都会生成新的临时工作目录,加载慢而且需要在代码中补充这部分的处理逻辑,因此不使用onefile模式
   - 每次重新生成exe之后,再次使用时需要删除原本的exe防火墙通信规则;其原因是Windows系统中防火墙存在的旧防火墙规则无法生效,导致会拦截重新生成的exe局域网通信,需要删除(控制面板\系统和安全\Windows Defender 防火墙\允许的应用)

### 用户使用注意事项

1. **网络环境要求**
   - 发信设备必须与服务端处于同一局域网
   - 确保防火墙允许配置的端口通信
   - Windows系统首次运行可能需要允许防火墙访问

2. **服务端运行注意事项**
   - 服务端运行期间不能修改配置项
   - 双击实时数据表格中的行可复制完整数据内容
   - 服务端支持定时运行，可配置每日运行时间段
   - 系统会自动清理过期数据和超量数据

3. **发信设备配置**
   - 设备标识长度必须在10-30个字符之间，只能包含字母和数字
   - 请求时必须在Header中包含`X-Client-Key`字段
   - 数据内容长度限制在1-100个字符之间

4. **API调用示例**
   ```bash
   # 获取令牌
   curl -H "Authorization: Bearer <api_key>" http://服务器IP:端口/webhook/token
   
   # 发送数据
   curl -X POST -H "Content-Type: application/json" \
        -H "X-Client-Key: <设备标识>" \
        -d '{"content":"数据内容"}' \
        http://服务器IP:端口/webhook/save
   
   # 获取未读数据
   curl -H "Authorization: Bearer <token>" \
        "http://服务器IP:端口/webhook/unread?size=10&client_key=<设备标识>"
   ```

5. **故障排除**
   - 如遇网络通信问题，可使用GUI界面的"网络通信修复"功能
   - 查看日志文件获取详细错误信息
   - 确保配置文件格式正确，特别是时区和IP白名单设置

6. **性能建议**
   - 建议定期清理过期数据，避免数据库过大影响性能
   - 合理设置数据存储上限，防止磁盘空间不足
   - 在高并发场景下，建议适当调整数据库连接池大小

## 测试说明

> 测试用例使用 `pytest` 库，不使用 `unittest` 库。

### 测试环境配置
```python
import logging
import os
import sys

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

logger = logging.getLogger(__name__)
```

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_webhook_server.py

# 运行测试并显示详细输出
pytest -v
``` 

## 项目特性

- **跨平台支持**: 支持Windows、Linux、macOS系统
- **多运行模式**: 支持GUI界面、命令行、配置选择界面三种运行模式
- **实时监控**: 提供CPU使用率、内存占用、进程状态等系统监控
- **数据安全**: 支持IP白名单、设备标识验证、API密钥认证
- **自动化管理**: 自动清理过期数据、日志文件，支持定时任务
- **多实例支持**: 支持多配置文件并发运行，防止配置冲突
- **用户友好**: 提供直观的GUI界面，支持配置验证和错误提示

## 技术栈

- **后端框架**: FastAPI + Uvicorn
- **数据库**: SQLite
- **GUI框架**: tkinter + ttkbootstrap
- **任务调度**: APScheduler
- **数据验证**: Pydantic
- **进程管理**: multiprocessing + psutil
- **网络处理**: aiohttp + requests

## 版本信息

- **当前版本**: 1.0.1
- **发布日期**: 2025-06-01
- **联系方式**: <EMAIL>

## 许可证

本软件遵循Apache License 2.0开源协议发布，用户应遵守相关法律法规和协议使用条款。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。在提交代码前，请确保：

1. 代码符合项目的编码规范
2. 添加必要的测试用例
3. 更新相关文档
4. 通过所有现有测试

## 更新日志

### v1.0.1 (2025-06-01)
- 初始版本发布
- 支持多设备数据接收和存储
- 提供GUI和命令行两种运行模式
- 实现配置管理和多实例支持
