"""Webhook服务器命令行启动模块。

此模块提供了Webhook服务器的命令行启动功能，支持通过命令行参数
指定配置文件路径来启动服务器。
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent) # noqa

# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
    sys.path.remove(src_path)
sys.path.insert(0,src_path)

from webhook_server.models import webhook_server
from common.utils.logging_utils import logger_print

if __name__ == "__main__":
    # 初始化临时logger用于命令行启动日志
    temp_logger: Optional[logging.Logger] = logging.getLogger(__name__)
    logger_print(
        msg="webhook server command line startup", custom_logger=temp_logger
    )
    parser = argparse.ArgumentParser(description="Webhook Server Command Line Interface")
    parser.add_argument(
        "--config", required=True, help="path to server config file"
    )
    args = parser.parse_args()
    logger_print(
        msg=f"parsed command line arguments: config={args.config}",
        custom_logger=temp_logger
    )
    asyncio.run(webhook_server.run_server_with_path(args.config))
