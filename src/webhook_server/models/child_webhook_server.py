import argparse
import asyncio
import sys
from pathlib import Path



src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent.parent) # noqa

# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
    sys.path.remove(src_path)
sys.path.insert(0,src_path)

from common import constants
from models.webhook_server import models


if __name__ == "__main__":
    """是直接给gui打开webhook server子进程的入口"""
    print(f"xxx:{constants.common_constants.BASE_PATH}")
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    asyncio.run(models.webhook_server.run_server_with_path(config_path=parser.parse_args().config, is_child_process=True))
