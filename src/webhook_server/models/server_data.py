"""服务端数据传输相关的类定义模块。

此模块包含了服务端数据传输相关的所有数据模型，包括：
- 数据传输模型
- 消息响应模型
- GUI显示模型
- 令牌异常类
"""

from datetime import datetime

from pydantic import BaseModel, Field

from common import constants


class SendData(BaseModel):
    """发信方发送数据到服务端的数据模型。

    Attributes:
        content: 消息内容，长度限制在1-100字符之间,当前场景不需要太长的消息内容
    """
    content: str = Field(..., min_length=1, max_length=100)


class MessageResponse(BaseModel):
    """服务端发送未读消息给第三方接收客户端的响应模型。

    Attributes:
        id: 消息唯一标识
        message: 消息内容
        client_key: 客户端标识
        reception_time: 接收时间
    """
    id: str
    message: str
    client_key: str
    reception_time: str


class MessageToGUI(BaseModel):
    """当前客户端界面显示的消息模型。

    Attributes:
        message: 消息内容
        client_key: 客户端标识
        reception_time: 接收时间
        is_read: 是否已读标识
    """
    message: str
    client_key: str
    reception_time: str
    is_read: int

class TokenException(Exception):
    """认证令牌异常基类。

    Attributes:
        error_code: 自定义错误码
        timestamp: 异常发生时间戳
    """

    def __init__(self, message: str, error_code: int = 4000) -> None:
        """初始化令牌异常。

        Args:
            message: 异常消息
            error_code: 错误码，默认为4000
        """
        super().__init__(message)
        self.error_code = error_code  # 自定义错误码
        self.timestamp = datetime.now().isoformat()

    def __str__(self) -> str:
        """返回格式化的异常字符串。"""
        return f"[{self.error_code}] {super().__str__()} @ {self.timestamp}"


class TokenExpiredError(TokenException):
    """令牌已过期异常（严重错误）。

    Attributes:
        expired_at: 令牌过期时间
    """

    def __init__(self, expired_at: datetime = None) -> None:
        """初始化令牌过期异常。

        Args:
            expired_at: 令牌过期时间，默认为当前时间
        """
        if expired_at is None:
            expired_at = datetime.now()
        msg = f"Token expired at {expired_at.strftime(constants.common_constants.DATETIME_FORMAT)}"
        super().__init__(msg, error_code=4101)
        self.expired_at = expired_at  # 令牌过期时间


class TokenNearExpirationWarning(TokenException):
    """令牌即将过期警告异常。

    Attributes:
        remain_seconds: 剩余秒数
    """

    def __init__(self, remain_seconds: int) -> None:
        """初始化令牌即将过期警告。

        Args:
            remain_seconds: 剩余秒数
        """
        msg = f"Token will expire in {remain_seconds} seconds"
        super().__init__(msg, error_code=2101)
        self.remain_seconds = remain_seconds
