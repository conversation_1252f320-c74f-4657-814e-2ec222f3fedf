"""服务端配置属性管理模块。

此模块负责服务端配置属性值的管理，包括：
- 配置文件解析
- 日志配置加载
- 数据管理器初始化
- 调度器配置
"""

import configparser
import logging
from datetime import datetime
from typing import Any, Dict, Optional
from zoneinfo import ZoneInfo

from apscheduler.schedulers.asyncio import AsyncIOScheduler

from common.utils import self_log, shutdown_exec_funct
from common.utils.logging_utils import logger_print
from common.utils.network_utils import get_whitelist
from common.utils.path_utils import get_real_exist_file_path
from common.utils.common_utils import str_to_bool, run_once
from common.utils.config_utils import section_to_dict
from webhook_server.config import config_check, constants
from webhook_server.models import server_data_manager
from webhook_server.utils import webhook_server_utils


class ServerProperties:
    """服务端配置属性管理类。

    负责解析和管理服务端配置文件，初始化相关组件如日志系统、
    数据管理器和任务调度器等。

    Attributes:
        logger: 日志记录器
        server_properties_path: 配置文件路径
        scheduler_default_config: 调度器默认配置
        scheduler: 异步调度器实例
        server_config: 服务器配置字典
        client_info_properties: 客户端信息配置
        server_config_zone: 服务器时区
        start_datetime: 启动时间
        data_manager: 数据管理器
    """

    def __init__(
        self,
        server_properties_path: str,
        custom_scheduler_config: dict = None
    ) -> None:
        """初始化服务端配置属性。

        Args:
            server_properties_path: 服务端配置文件路径
            custom_scheduler_config: 自定义调度器配置，可选
        """
        self.logger: Optional[logging.Logger] = (
            logging.getLogger(__name__) if self_log.log_config.had_init() else None
        )
        logger_print(
            msg=f"initializing server properties with path: {server_properties_path}",
            custom_logger=self.logger
        )
        properties_path = get_real_exist_file_path(server_properties_path)
        logger_print(
            msg=f"this complete cultural properties path: {properties_path}",
            custom_logger=self.logger
        )

        self.server_properties_path = properties_path
        self.scheduler_default_config = (
            custom_scheduler_config if custom_scheduler_config is not None
            else constants.DEFAULT_SCHEDULER_CONFIG
        )
        logger_print(
            msg=f"scheduler config set: {self.scheduler_default_config}",
            custom_logger=self.logger
        )
        self.scheduler: Optional[AsyncIOScheduler] = None

        logger_print(msg="parsing properties", custom_logger=self.logger)
        self._parse_properties()
        logger_print(msg="properties parsed successfully", custom_logger=self.logger)

        logger_print(msg="initializing other components", custom_logger=self.logger)
        self._init_other()
        logger_print(msg="other components initialized", custom_logger=self.logger)

        self.start_datetime = datetime.now(self.server_config_zone)
        logger_print(msg=f"server properties initialization completed at: {self.start_datetime}", custom_logger=self.logger)

    def _parse_properties(self):
        config_check.common_check_config_file(self.server_properties_path)
        config = configparser.ConfigParser(interpolation=None)
        # key保持原本的大小写
        config.optionxform = str
        logger_print(msg=f"reading config file: {self.server_properties_path}", custom_logger=self.logger)
        config.read(self.server_properties_path, encoding="utf-8")
        logger_print(msg="config file read successfully", custom_logger=self.logger)

        # 检查配置文件[server]中配置项是否合法,然后转换类型
        logger_print(msg="parsing server config section", custom_logger=self.logger)
        server_properties = ServerProperties.parse_server_section(config)

        logger_print(msg="parsing client info section", custom_logger=self.logger)
        client_info_properties = ServerProperties.parse_client_section(config)

        # 该字段有效性已经进行检查
        self.server_config_zone = ZoneInfo(server_properties["time_zone"])
        logger_print(msg=f"timezone set successfully: {self.server_config_zone}", custom_logger=self.logger)

        self.server_config = server_properties
        self.client_info_properties = client_info_properties
        logger_print(msg="properties parsing completed", custom_logger=self.logger)


    @staticmethod
    def parse_client_section(config: configparser.ConfigParser) -> Dict[str, Any]:
        client_info_properties = section_to_dict(config, "client_info")
        webhook_server_utils.check_all_client_info_config(client_info_properties)
        return client_info_properties

    # 检查配置文件[server]中配置项是否合法,然后转换类型
    @staticmethod
    def parse_server_section(config: configparser.ConfigParser) -> Dict[str, Any]:
        server_config = section_to_dict(config, "server")
        ServerProperties.validate_and_convert_config(server_config)
        return server_config

    @staticmethod
    def validate_and_convert_config(server_config: dict):
        missing = constants.SERVER_REQUIRED_KEYS - server_config.keys()
        if missing:
            raise ValueError(f"server properties [server] missing required keys: {missing}")
        # 由于之前key和value都已经trim操作了,所以这里方法中对应配置项不需要再次trim
        # 其中字段如果已经不是字符串类型,执行了一次该函数进行类型转换,就不需要进行字符串类型相关操作
        # 校验配置项是否合法
        section='server'
        for key in constants.SERVER_REQUIRED_KEYS:
            config_check.check_config_section_key_value_format_valid(section=section, key=key, value=server_config.get(key))

        # 配置项格式转换
        server_config['whitelist']= get_whitelist(server_config['whitelist'])
        server_config['log_config_path']= get_real_exist_file_path(server_config['log_config_path'])
        server_config['port'] = int(server_config['port'])
        server_config['expire_data_days']=int(server_config['expire_data_days'])
        server_config['data_limit_num']=int(server_config['data_limit_num'])
        server_config['enable_sql_logging']= str_to_bool(server_config['enable_sql_logging'])


    # 外部最后必须调用
    def clear(self):
        run_once(self._clear)

    def _clear(self):
        logger_print(msg="server properties clear ...", custom_logger=self.logger)
        try:
            if hasattr(self, "scheduler") and self.scheduler is not None and self.scheduler.running:
                try:
                    # 由于logging模块会在atexit时关闭,导致其不能使用,所以就关闭了调度器中的logger使用
                    self.scheduler._logger.setLevel(100) # noqa
                    self.scheduler.shutdown(wait=True)
                except BaseException as e:
                    logger_print(msg=f"error shutting down scheduler!", custom_logger=self.logger, use_exception=True, exception=e)
                finally:
                    self.scheduler = None
        finally:
            if hasattr(self, "data_manager") and self.data_manager is not None:
                self.data_manager = None
            self.start_datetime = None
            self.client_info_properties.clear()
            self.server_config.clear()
            self.server_config_zone = None
            self.server_properties_path = None


    # 初始化其他操作[如日志配置等]
    def _init_other(self):
        # 初始化定时任务调度器
        self.scheduler = AsyncIOScheduler(job_defaults=self.scheduler_default_config, timezone=self.server_config_zone)
        logger_print(msg=f"scheduler created !", custom_logger=self.logger)

        shutdown_exec_funct.register(self.clear)
        self_log.setup_logging(self.server_config["log_config_path"], time_zone= self.server_config_zone, filename_prefix=self.server_config["app_name"])
        if self.logger is None:
            self.logger = logging.getLogger(__name__)
        logger_print(msg="logging setup completed", custom_logger=self.logger)
        message_data_table_name=self.server_config['message_data_table_name']
        enable_sql_logging=self.server_config['enable_sql_logging']
        self.data_manager = server_data_manager.WebhookDataManager(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH,table_name=message_data_table_name, zone=self.server_config_zone, enable_sql_logging=enable_sql_logging)
        logger_print(msg="data manager initialized successfully", custom_logger=self.logger)

