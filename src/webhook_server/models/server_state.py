import asyncio
import logging
from typing import Optional

import uvicorn

from common import utils
from common.utils import common_utils
from common.utils import logging_utils
from webhook_server import models


# 服务端状态
class ServerState:
    __slots__ = ('end_flag', 'properties', 'webhook_server', 'server_refresh_token')

    def __init__(self):
        self.end_flag: asyncio.Event = asyncio.Event()
        self.properties: Optional[models.server_properties.ServerProperties]= None
        self.webhook_server: Optional[uvicorn.Server] = None
        self.server_refresh_token: Optional[str]  = None

    # 关闭时的清除操作
    def reset_clear(self):
        common_utils.run_once(self._reset_clear)

    def _reset_clear(self):
        # 使用临时logger进行日志记录
        temp_logger: Optional[logging.Logger] = logging.getLogger(__name__) if utils.self_log.log_config.had_init() else None
        logging_utils.logger_print(msg="webhook server is going to reset clear  ......", custom_logger=temp_logger)

        self.end_flag.set()

        if hasattr(self,'properties') and self.properties is not None:
            self.properties.clear()
            self.properties = None
            logging_utils.logger_print(msg="server properties cleared", custom_logger=temp_logger)
        else:
            logging_utils.logger_print(msg="no server properties to clear", custom_logger=temp_logger)

        if hasattr(self,'webhook_server') and self.webhook_server is not None:
            self.webhook_server.should_exit = True
            self.webhook_server = None
            logging_utils.logger_print(msg="webhook server cleared", custom_logger=temp_logger)
        else:
            logging_utils.logger_print(msg="no webhook server to clear", custom_logger=temp_logger)

        logging_utils.logger_print(msg="clearing server refresh token", custom_logger=temp_logger)
        self.server_refresh_token = None
        logging_utils.logger_print(msg="server reset clear process completed", custom_logger=temp_logger)
