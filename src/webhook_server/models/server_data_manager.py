# 使用SQLite来保证对应服务端中流转的数据的完整性和安全性,以下是对应的操作函数[存储到数据库中的时间字段是默认的UTC,但是给其他查看时会转换成自己设置的时区的时间]:
import logging
import os
import sqlite3
from typing import Optional, List
from zoneinfo import ZoneInfo

import ulid

from common import models
from common.utils import common_utils
from common.utils import path_utils
from webhook_server import config
from webhook_server import models

logger = logging.getLogger(__name__)

class WebhookDataManager(models.sqlite_base_manager.SQLiteBaseManager):
    """
    注意事项:
        1. 本表中reception_time字段需要转换成对应时区格式才能给外部使用
        2. 数据存储的表由初始时传入的table_name参数决定
    """
    def __init__(self, db_path: str,table_name:str, zone: ZoneInfo = None, enable_sql_logging=config.gui_constants.ENABLE_SQL_LOGGING):
        WebhookDataManager._check_init_params(db_path=db_path,table_name=table_name)
        logger.info(f"initializing webhook data manager with db_path: {db_path}")
        resolved_path = path_utils.get_real_path_create_dir(db_path)
        logger.info(f"resolved database path: {resolved_path}")
        self.message_data_table_name = table_name
        super().__init__(db_path=resolved_path, enable_sql_logging=enable_sql_logging,zone=zone)

        logger.info("sqlite base manager initialized")


        logger.info(f"timezone set to: {self.zone}")
        logger.info("webhook data manager initialization completed")

    @staticmethod
    def _check_init_params(db_path:str,table_name:str):
        if not db_path or not table_name or not isinstance(db_path,str) or not isinstance(table_name,str):
            raise ValueError("db_path and table_name must be non-empty string")
        if os.path.exists(db_path) and not os.path.isfile(db_path):
            raise ValueError("db_path must be a file path")

    # 初始化数据库表结构并设置WAL/NORMAL模式
    def _init_db(self):
        # 初始化数据库:[新建表,创建索引,设置WAL/NORMAL模式]
        # , factory=sql_log.LoggingConnection --- 打印具体执行的SQL语句
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("PRAGMA journal_mode=WAL;")
            conn.execute("PRAGMA synchronous=NORMAL;")
            init_sql = """
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id TEXT NOT NULL PRIMARY KEY,
                    message TEXT NOT NULL CHECK(length(message) BETWEEN {min_message_len} AND {max_message_len}),
                    client_key TEXT NOT NULL CHECK(length(client_key) BETWEEN {min_client_key_len} AND {max_client_key_len}),
                    reception_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    is_read INTEGER NOT NULL DEFAULT 0 CHECK(is_read IN (0, 1))
                );
                CREATE INDEX IF NOT EXISTS idx_read_clientkey ON {table_name}(is_read, client_key);
                CREATE INDEX IF NOT EXISTS idx_time ON {table_name}(reception_time);
                CREATE INDEX IF NOT EXISTS idx_read_time ON {table_name}(is_read, reception_time);
                CREATE INDEX IF NOT EXISTS idx_client_read_time ON {table_name}(client_key, is_read, reception_time);
                """.format(table_name=self.message_data_table_name,min_message_len=config.constants.MIN_MESSAGE_LEN, max_message_len=config.constants.MAX_MESSAGE_LEN,
                           min_client_key_len=config.constants.MIN_CLIENT_KEY_LEN,
                           max_client_key_len=config.constants.MAX_CLIENT_KEY_LEN)
            conn.executescript(init_sql)
            conn.commit()


    # 保存接收到的消息
    def save_message(self, message: str, client_key: str) -> str:
        logger.info(f"saving message from client_key: {client_key}")
        logger.info(f"original message length: {len(message) if message else 0}")

        # 消息长度校验在事务开始前完成
        message = common_utils.trim(message)

        if message is None or not (config.constants.MIN_MESSAGE_LEN <= len(message) <= config.constants.MAX_MESSAGE_LEN):
            raise ValueError(
                f"the effective length range of the received data must be between {config.constants.MIN_MESSAGE_LEN}-{config.constants.MAX_MESSAGE_LEN} characters!")

        logger.info("message length validation passed")
        # 不需要校验client_key,已经在加载配置属性时就已经进行了校验
        # 为了保证重试时不会重复插入,这里必须保证幂等性
        message_id = ulid.new().str
        logger.info(f"generated message id: {message_id}")

        logger.info("executing database insert")
        insert_sql=f"INSERT INTO {self.message_data_table_name} (id,message, client_key) VALUES (?,?, ?);"
        self._exec_retry(lambda cur: cur.execute(insert_sql,(message_id, message, client_key)))
        logger.info(f"message saved successfully with id: {message_id}")
        return message_id

    def _execute_read(self,cursor: sqlite3.Cursor, query_sql: str, params: tuple) -> List[models.server_data.MessageResponse]:
        """
        读取消息并将对应的数据标记为已读
        使用该函数的注意事项:
            1. query_sql的字段顺序必须是:id,message,client_key,reception_time
            2. query_sql中必须包含limit参数
        :param cursor:
        :param query_sql: 执行的查询sql语句
        :param params: sql语句中的参数
        :return:
        """
        cursor.execute(query_sql, params)
        rows = cursor.fetchall()

        if not rows:
            logger.warning(f"no unread data received for query: {query_sql}, params: {params}")
            return []

        ids = [row[0] for row in rows]
        for i in range(0, len(ids), config.constants.SQLITE_VARIABLE_LIMIT):
            had_read_ids = ids[i:i + config.constants.SQLITE_VARIABLE_LIMIT]
            placeholders = ','.join('?' * len(had_read_ids))
            update_sql=f"UPDATE {self.message_data_table_name} SET is_read = 1 WHERE id IN ({placeholders}) AND is_read = 0;"
            cursor.execute(update_sql,had_read_ids)

        # 其中将数据库默认的UTC时区转换成自己的时区
        return [models.server_data.MessageResponse(id=row[0], message=row[1], client_key=row[2],
                                            reception_time=WebhookDataManager.convert_utc_str(row[3], self.zone)) for row in
                rows]

    # 读取消息传递的参数的校验[内部函数] size:一次能够读取的消息数量,client_key:对应发送方的标识符
    @staticmethod
    def _check_read_msg_params(client_key: str|None, size: int) -> str|None:
        if not (config.constants.MIN_UNREAD_MSGS_LEN <= size <= config.constants.MAX_UNREAD_MSGS_LEN):
            raise ValueError(
                f"the range of unread data allowed to be read at once must be {config.constants.MIN_UNREAD_MSGS_LEN}-{config.constants.MAX_UNREAD_MSGS_LEN}")
        return common_utils.trim(client_key)

    # 获取最新的size条消息:包含已读和未读的消息 --- 给服务端gui界面显示使用【不能给客户端使用】
    def get_recent_data(self, size: int) -> List[models.server_data.MessageToGUI]:
        logger.info(f"getting recent data with size: {size}")
        WebhookDataManager._check_read_msg_params(None, size)
        logger.info("parameters validated for recent data query")

        query_sql = f"""
            SELECT  message, client_key, reception_time, is_read
            FROM {self.message_data_table_name}
            ORDER BY reception_time DESC
            LIMIT ?;"""
        result = self._exec_retry(lambda cur: cur.execute(query_sql, (size,)).fetchall())
        if not result:
            return []

        # 转换 reception_time 字段为对应时区格式
        result = [models.server_data.MessageToGUI(message=row[0], client_key=row[1],
                                                  reception_time=WebhookDataManager.convert_utc_str(row[2], self.zone),is_read=row[3]) for
                      row in result]
        logger.info(f"retrieved {len(result)} recent data records")
        return result
    # 获取最旧的未读size条消息:[不需要进行client_key的长度校验,其client_key出现错误时就不会得到结果]
    def get_oldest_unread(self, size: int, client_key: Optional[str] = None) -> List[models.server_data.MessageResponse]:
        client_key = WebhookDataManager._check_read_msg_params(client_key, size)
        query_sql = f"""
            SELECT id, message, client_key, reception_time 
            FROM {self.message_data_table_name} 
            WHERE is_read = 0 
            AND (client_key = ? OR ? IS NULL)
            ORDER BY reception_time ASC 
            LIMIT ?;"""
        def wrapper_function(cursor: sqlite3.Cursor):
            return self._execute_read(cursor, query_sql=query_sql, params=(client_key, client_key, size))
        return self._exec_retry(wrapper_function)

    # 获取最新minutes分钟内的未读size条消息
    def get_recent_unread(self, size: int, minutes: int, client_key: Optional[str] = None) -> List[models.server_data.MessageResponse]:
        logger.info(f"getting recent unread messages: size={size}, minutes={minutes}, client_key={client_key}")

        if not (config.constants.EARLIEST_RECENTLY_UNREAD_TIME <= minutes ):
            logger.error(f"invalid minutes parameter: {minutes}, must be greater than or equal to {config.constants.EARLIEST_RECENTLY_UNREAD_TIME}")
            raise ValueError(
                f"when obtaining unread data within the specified recent time, the specified time must be  greater than or equal to {config.constants.EARLIEST_RECENTLY_UNREAD_TIME} minutes")

        logger.info("minutes parameter validation passed")
        client_key = WebhookDataManager._check_read_msg_params(client_key, size)
        logger.info(f"validated client_key: {client_key}")

        query_sql = f"""
            SELECT id, message, client_key, reception_time
            FROM {self.message_data_table_name}
            WHERE is_read = 0
            AND (client_key = ? OR ? IS NULL)
            AND reception_time >= datetime('now', '-' || ? || ' minutes')
            ORDER BY reception_time DESC
            LIMIT ?;"""
        logger.info("executing recent unread query")
        def wrapper_function(cursor: sqlite3.Cursor):
            return self._execute_read(cursor, query_sql=query_sql, params=(client_key, client_key, minutes, size))
        result = self._exec_retry(wrapper_function)
        logger.info(f"retrieved {len(result)} recent unread messages")
        return result

    # 获取超过[days]天数的未读消息数量
    def get_older_unread_count(self, days: int) -> int:
        if days < 1:
            raise ValueError("the specified days must be greater than 0")
        query_sql = f"""
            SELECT COUNT(*)
            FROM {self.message_data_table_name}
            WHERE is_read = 0
            AND reception_time <= datetime('now', '-' || ? || ' days');"""
        return self._exec_retry(lambda cur: cur.execute(query_sql, (days,)).fetchone()[0])

    #
    def _remove_expired_read_data(self,cursor: sqlite3.Cursor, expire_data_days: int):
        """"
        删除过期expire_data_days天的已读数据,在数据量过大时,一次删除1000条数据
        """
        delete_sql = f"""
            DELETE FROM {self.message_data_table_name}
            WHERE id in (
                        SELECT id FROM {self.message_data_table_name}  WHERE is_read = 1  AND date(reception_time) < date('now', '-' || ? || ' days') LIMIT 1000
                        )
                    ;"""
        while True:
            cursor.execute(delete_sql, (expire_data_days,))
            if cursor.rowcount == 0:
                break
            else:
                logger.info(f"deleted {cursor.rowcount} expired read data records")

    def remove_expired_read_data(self, expire_data_days):
        # 当所允许的数据库中过期时间是非正数时,则数据永不过期
        if expire_data_days < 1:
            logger.warning(f"the specified expire data days is less than 1, no expired data will be removed")
            return
        def wrapper_function(cursor: sqlite3.Cursor):
            self._remove_expired_read_data(cursor, expire_data_days=expire_data_days)
        self._exec_retry(wrapper_function)

    # 删除超出限制的已读数据
    def _remove_excess_read_data(self, cursor: sqlite3.Cursor, data_limit_num: int):
        """
        删除超出限制的已读数据
        如果总数据量没有超过data_limit_num，无论其中是否包含已读数据都不进行处理
        如果总数超过data_limit_num，那么所有未读数据不变，删除总数量在data_limit_num外已读数据
        :param cursor:
        :param data_limit_num: 数据库表保留的最大数据条数
        :return:
        """
        # 1. 检查总数据量
        cursor.execute(f"SELECT COUNT(*) FROM {self.message_data_table_name}")
        total_count = cursor.fetchone()[0]
        
        if total_count <= data_limit_num:
            return  # 总数未超过限制，不需要删除
        
        # 2. 获取未读数据数量
        cursor.execute(f"SELECT COUNT(*) FROM {self.message_data_table_name} WHERE is_read = 0")
        unread_count = cursor.fetchone()[0]
        if total_count==unread_count:
            return  # 全部数据都是未读，不需要删除
        
        # 3. 计算需要保留的已读数据数量
        keep_read_count = max(0, data_limit_num - unread_count)
        
        # 4. 获取需要保留的已读数据ID（按时间倒序取最新的）
        if keep_read_count > 0:
            cursor.execute(f"SELECT id FROM {self.message_data_table_name} WHERE is_read = 1 ORDER BY reception_time DESC LIMIT ?", (keep_read_count,))
            keep_read_ids = [row[0] for row in cursor.fetchall()]
            
            # 5. 删除不在保留列表中的已读数据
            if keep_read_ids:
                placeholders = ','.join('?' * len(keep_read_ids))
                delete_sql = f"DELETE FROM {self.message_data_table_name} WHERE is_read = 1 AND id NOT IN ({placeholders})"
                cursor.execute(delete_sql, keep_read_ids)
            else:
                # 如果没有需要保留的已读数据，删除所有已读数据
                cursor.execute(f"DELETE FROM {self.message_data_table_name} WHERE is_read = 1")
        else:
            # 如果不需要保留任何已读数据，删除所有已读数据
            cursor.execute(f"DELETE FROM {self.message_data_table_name} WHERE is_read = 1")
        
        if cursor.rowcount > 0:
            logger.info(f"deleted {cursor.rowcount} excess read data records")

    def remove_excess_read_data(self, data_limit_num: int):
        # 当所允许的数据库中条数是非正数时,则不限制数据数量
        if data_limit_num < 1:
            logger.warning(f"the specified data limit number is less than 1, no excess data will be removed")
            return
        if data_limit_num >= config.constants.MAX_MESSAGE_COUNT:
            data_limit_num = config.constants.MAX_MESSAGE_COUNT - 1
        logger.warning(f"the specified data limit number actually is {data_limit_num}")
        def wrapper_function(cursor: sqlite3.Cursor):
            self._remove_excess_read_data(cursor, data_limit_num=data_limit_num)
        self._exec_retry(wrapper_function)
