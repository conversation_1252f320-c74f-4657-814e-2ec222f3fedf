import logging
import tkinter
from tkinter import messagebox
from typing import Union

from common.constants import common_constants
from common.utils.logging_utils import logger_print
from common.utils.system_utils import remove_firewall_rule_with_windows, restart_cur_program
from webhook_server.config import gui_constants

logger = logging.getLogger(__name__)

def restore_network_connection(parent_win:Union[tkinter.Tk, tkinter.Toplevel]):
    """点击网络修复且是Windows系统时，删除当前应用的防火墙规则"""
    if not messagebox.askyesno(title="网络通信修复提示", message="当前网络通信修复功能仅支持Windows系统\n修复之后会进行程序重启,且再次点击服务端启动按钮时需要同意Windows弹窗[申请网络通信权限]\n是否继续？",parent=parent_win):
        return
    if not common_constants.IS_WINDOWS:
        messagebox.showwarning(title="提示", message="当前程序网络通信修复功能仅支持Windows系统,其他操作系统稍后支持",parent=parent_win)
        return
    remove_firewall_rule_with_windows(gui_constants.SPEC_SOFTWARE_NAME)
    logger_print(msg=f"remove firewall rule {gui_constants.SPEC_SOFTWARE_NAME} success", custom_logger=logger)
    # 程序重启
    restart_cur_program()
