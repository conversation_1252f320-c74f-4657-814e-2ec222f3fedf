"""多进程配置管理模块。

此模块提供了多进程环境下配置文件的唯一性管理功能，包括：
- 配置文件占用状态管理
- 进程间配置文件互斥访问
- 心跳机制和资源清理
- 配置文件有效性检测
"""

import configparser
import logging
import sqlite3
import threading
import time
from typing import Dict, List, Optional, Tuple
from zoneinfo import ZoneInfo

import psutil

from common.models import sqlite_base_manager, base_config_unique_manage
from common.utils.logging_utils import logger_print
from common.utils.path_utils import file_hash, get_real_path
from common.utils.config_utils import synch_section, update_config
from ..config import config_check, constants, gui_constants

logger = logging.getLogger(__name__)

class MultiProcessConfigManager(base_config_unique_manage.BaseConfigUniquenessManager):
    """多进程配置文件唯一性管理器（单例模式）。

    注意事项:
        1. 由于本类可以前端界面显示也可以后端服务端使用,所以直接抛出异常给调用方使用
        2. 本数据库表中时间字段如果需要给前端显示,则需要进行时区转换,
           本类中 CURRENT_TIMESTAMP 存储的是UTC时区,需要转换

    主要功能：
        1. 管理配置文件的占用状态，确保同一配置文件不被多个进程同时使用
        2. 维护配置文件记录的有效性检测
        3. 提供配置文件注册、注销、更新功能
        4. 心跳机制保证进程存活检测和资源清理
    """

    HEARTBEAT_TIMEOUT = 300  # 5分钟心跳超时 [s]
    HEARTBEAT_INTERVAL = 15  # 15秒心跳间隔 [s]

    def __init__(self, db_path: str = constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging: bool = True, zone: ZoneInfo = None):
        logger_print(msg=f"initializing multiprocess UI config manager with db_path: {db_path}, sql_logging: {enable_sql_logging}", custom_logger=logger)
        super().__init__(
            db_path=db_path,
            enable_sql_logging=enable_sql_logging,
            heartbeat_interval=MultiProcessConfigManager.HEARTBEAT_INTERVAL,
            heartbeat_timeout=MultiProcessConfigManager.HEARTBEAT_TIMEOUT,
            zone=zone
        )
        logger_print(msg="multiprocess UI config manager initialized", custom_logger=logger)
        self._config_lock = threading.RLock()  # 配置操作的并发锁
        self._registered_config_path: Optional[str] = None  # 当前进程注册的配置文件路径
        self._registered_config_id: Optional[int] = None    # 当前进程注册的配置记录ID
        self._is_registered = False  # 标记当前进程是否已注册配置

    def _init_db(self):
        """初始化数据库表结构"""
        def init_tables(cur: sqlite3.Cursor):
            # 创建配置文件使用记录表
            init_sql = """
            CREATE TABLE IF NOT EXISTS config_file_record (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,                   -- 配置加载界面显示的名称
                file_path TEXT NOT NULL UNIQUE,       -- 文件绝对路径
                process_id INTEGER,                   -- 占用进程PID
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 最后使用时间
                checksum TEXT NOT NULL,               -- SHA-256文件哈希
                is_active INTEGER NOT NULL DEFAULT 1 CHECK(is_active IN (0, 1)), -- 当前配置是否有效
                fail_reason TEXT,                     -- 当前配置失效原因
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- 更新时间
            );
            
            CREATE TABLE IF NOT EXISTS config_unique_constraints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_file_record_id INTEGER NOT NULL,  -- 关联 config_file_record.id
                config_key TEXT NOT NULL,            -- 配置键
                config_value TEXT NOT NULL,          -- 配置值
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP   -- 更新时间
            );
            
            -- 优化索引
            CREATE INDEX IF NOT EXISTS idx_file_path ON config_file_record (file_path);
            CREATE INDEX IF NOT EXISTS idx_process_id ON config_file_record (process_id);
            CREATE INDEX IF NOT EXISTS idx_last_used ON config_file_record (last_used);
            CREATE INDEX IF NOT EXISTS idx_is_active ON config_file_record (is_active);
            
            CREATE INDEX IF NOT EXISTS idx_config_file_record_id ON config_unique_constraints (config_file_record_id);
            CREATE INDEX IF NOT EXISTS idx_config_key_value ON config_unique_constraints (config_key, config_value);
            """
            cur.executescript(init_sql)

        self._exec_retry(init_tables)
        logger_print(msg="multiprocess UI config database tables initialized", custom_logger=logger)

    def _start_heartbeat_thread(self):
        """启动心跳线程（仅当已注册配置时运行）"""
        with self._config_lock:
            if not self._is_registered:
                return
            super()._start_heartbeat_thread()
    def _update_heartbeat(self):
        """心跳机制内更新数据库记录当前记录最后时间"""
        with self._config_lock:
            if not self._is_registered or not self._registered_config_path:
                return
        def _update(cur: sqlite3.Cursor):
            heartbeat_sql = "UPDATE config_file_record SET last_used = CURRENT_TIMESTAMP,updated_at = CURRENT_TIMESTAMP WHERE id = ? ;"
            cur.execute(heartbeat_sql, (self._registered_config_id,))

        self._exec_retry(_update)
        logger_print(msg=f"updated heartbeat for config: {self._registered_config_path}", custom_logger=logger, log_level=logging.DEBUG)
    def _heartbeat_worker(self):
        """心跳工作线程"""
        while not self._stop_heartbeat_event.is_set():
            try:
                with self._config_lock:
                    if not self._is_registered:
                        logger_print(msg="no registered config, stopping heartbeat", custom_logger=logger)
                        break

                self._update_heartbeat()
                self._release_validate_cleanup_invalid_records()

                with self._config_lock:
                    self._heartbeat_failure_count = 0  # 重置失败计数器
                logger_print(msg=f"heartbeat thread running, interval: {self._heartbeat_interval}s", custom_logger=logger, log_level=logging.DEBUG)
                start_time = time.time()
                if self._stop_heartbeat_event.wait(timeout=self._heartbeat_interval):
                    break
                logger_print(msg=f"gui heartbeat worker running, elapsed time: {time.time() - start_time}", custom_logger=logger, log_level=logging.DEBUG)
            except Exception as heartbeat_ex:
                with self._config_lock:
                    self._heartbeat_failure_count += 1
                    cur_failure_count = self._heartbeat_failure_count
                logger_print(msg=f"heartbeat thread error: {heartbeat_ex}", custom_logger=logger, log_level=logging.ERROR)

                # 连续失败多次后重启线程
                if cur_failure_count >= 3:
                    logger_print(msg="heartbeat thread failed 3 times, attempting to restart thread", custom_logger=logger, log_level=logging.ERROR)
                    self._restart_heartbeat_thread()
                    return
                if self._stop_heartbeat_event.wait(timeout=5):# 出错后等待5秒再重试
                    break

        logger_print(msg="gui heartbeat worker stopped", custom_logger=logger)
    @staticmethod
    def _release_fake_process_config(cur: sqlite3.Cursor):
        """
        释放被假进程占用的配置
        过滤机制:
        1. 对应进程检测已经死亡
        和"心跳超时"无关,进程死亡就是进程死亡,就释放对应占用的配置文件
        """
        # 获取所有数据库中占用配置的PID
        get_all_process_ids_sql = "SELECT process_id FROM config_file_record WHERE process_id IS NOT NULL;"
        cur.execute(get_all_process_ids_sql)
        db_pids = {row[0] for row in cur.fetchall() if row[0] is not None}
        if not db_pids:
            return
        # 找出已终止的PID
        dead_pids =  {pid for pid in db_pids if  not  psutil.pid_exists(pid)}
        # 释放已死进程占用的配置
        if dead_pids:
            placeholders = ",".join('?' * len(dead_pids))
            set_no_pid_sql = f"UPDATE config_file_record SET process_id = NULL,updated_at = CURRENT_TIMESTAMP WHERE process_id IN ({placeholders});"
            cur.execute(set_no_pid_sql, tuple(dead_pids))
            logger_print(f"released unused config for dead processes: {dead_pids}", custom_logger=logger, log_level=logging.DEBUG)
    def _cleanup_inactive_records(self, cur: sqlite3.Cursor):
        """清理失效的数据记录 --- 补充操作,一般在设置数据失效的情况下同步会删除失效的唯一约束记录"""
        # config_file_record 表数据不删除,后台不删除该数据,而是用户手动调用删除操作
        delete_inactive_records_sql = "DELETE FROM config_unique_constraints WHERE config_file_record_id IN (SELECT id FROM config_file_record WHERE is_active = 0);"
        cur.execute(delete_inactive_records_sql)
        cur_deleted_count = cur.rowcount
        delete_invalid_config_records_sql = "DELETE FROM config_unique_constraints WHERE config_file_record_id NOT IN (SELECT id FROM config_file_record);"
        cur.execute(delete_invalid_config_records_sql)
        cur_deleted_count += cur.rowcount
        if cur_deleted_count>0:
            logger_print(f"released invalid config records - unique_constraints!", custom_logger=logger)

    def ___release_validate_cleanup_invalid_records(self,cur: sqlite3.Cursor):
        """该函数实际使用的场景是刷新和心跳机制中,其他地方不需要使用该方法,如果出现判断唯一性配置项冲突,那就就行刷新再次即可,不会将无效数据放入到数据库中"""
        # 释放假进程占用的配置
        MultiProcessConfigManager._release_fake_process_config(cur)
        # 检测未被占用配置的有效性
        self._validate_unoccupied_valid_configs(cur)
        self._cleanup_inactive_records(cur)
    def _release_validate_cleanup_invalid_records(self):
        """
        释放假进程占用的配置并检测未被占用配置的有效性并清除失效的记录
        --- 一般在数据存在变化前使用
        使用注意事项: 不能内嵌到(cur: sqlite3.Cursor函数内)
        使用场景: 刷新和心跳机制中,其他场景不需要使用该函数
        """
        self._exec_retry(self.___release_validate_cleanup_invalid_records)
    def _check_one_record_valid(self, cur: sqlite3.Cursor,config_file_record_id, file_path, stored_checksum,need_raise=True):
        """
        检测单个已经存在于数据库中的配置文件逻辑是否有效,在该记录失效的情况下会修改记录中有效性和去除唯一性配置项记录
        :param cur:
        :param config_file_record_id: 数据库中配置文件记录ID
        :param file_path: 配置文件绝对路径
        :param stored_checksum: 数据库中存储的配置文件哈希值
        :param need_raise: 是否需要抛出异常
        :return: 无返回值
        """
        try:
            # 检查文件有效性
            config_check.check_loading_gui_list_config_file(file_path, stored_checksum)
        except Exception as error_reason:
            # 当前配置文件失效,标记为无效
            self._update_invalid_config_state(file_path, config_file_record_id, str(error_reason), cur)
            if need_raise:
                raise

    def _validate_unoccupied_valid_configs(self, cur: sqlite3.Cursor):
        """检测未被占用且有效的配置此刻的有效性"""
        # 获取所有未被占用且标记为有效的配置
        get_valid_configs_sql = "SELECT id, file_path, checksum FROM config_file_record WHERE process_id IS NULL AND is_active = 1;"
        cur.execute(get_valid_configs_sql)

        configs_to_validate = cur.fetchall()

        for config_file_record_id, file_path, stored_checksum in configs_to_validate:
            self._check_one_record_valid(cur,config_file_record_id, file_path, stored_checksum,False)


    def _update_invalid_config_state(self, file_path:str, config_file_record_id: int, error_reason: str, cur: sqlite3.Cursor):
        """标记配置为无效并清理相关配置项唯一记录"""
        with self._config_lock:
            set_record_invalid_sql = "UPDATE config_file_record SET is_active = 0, fail_reason = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? ;"
            cur.execute(set_record_invalid_sql, (error_reason, config_file_record_id))

            # 清理对应的配置项约束记录
            delete_unique_constraints_sql = "DELETE FROM config_unique_constraints WHERE config_file_record_id = ? ;"
            cur.execute(delete_unique_constraints_sql, (config_file_record_id,))

            # 替换logger.warning
            logger_print(msg=f"marked config {file_path} as invalid: {error_reason}", custom_logger=logger, log_level=logging.WARNING)

    def _get_all_config_records_to_by_status(self,cur: sqlite3.Cursor)->Tuple[List[Dict],List[Dict],List[Dict]]:
        """获取此刻所有配置记录并区分出无效和有效/有效未被占用的记录:刷新列表返回的配置记录数据"""
        invalid_records=[]
        valid_records=[]
        used_by_process_records=[]
        get_all_records_sql="SELECT id, name, file_path, process_id, last_used,is_active, fail_reason FROM config_file_record ORDER BY last_used DESC;"
        cur.execute(get_all_records_sql)
        result = cur.fetchall()
        if result:
            columns = [desc[0] for desc in cur.description]
            for row in result:
                record = dict(zip(columns, row))
                # last_used 时间戳时区转换转换当前时区
                record['last_used'] = sqlite_base_manager.SQLiteBaseManager.convert_utc_str(record['last_used'], self.zone)
                if record['is_active']==0:
                    invalid_records.append(record)
                elif record['process_id'] is None:
                    valid_records.append(record)
                else:
                    used_by_process_records.append(record)
        return invalid_records, valid_records, used_by_process_records
    @staticmethod
    def _extract_unique_constraints_from_config_file(file_path: str) -> dict[str, str]:
        """
        从配置文件中提取需要唯一性约束的配置项key-value对
        Returns:
            List of (config_key, config_value) tuples
        """
        server_config = configparser.ConfigParser(interpolation=None)
        server_config.read(file_path, encoding="utf-8")
        unique_constraints = {}
        # 所有的唯一性配置项都在server节点中
        section='server'
        for key in gui_constants.SERVER_CONFIG_UNIQUE_KEYS:
            if server_config.has_option(section, key):
                value = server_config.get(section, key)
                unique_constraints[key]= value
            elif key not in gui_constants.USER_CUSTOM_KEYS:
                logger_print(f"config item {key} not found in {file_path}", custom_logger=logger, log_level=logging.WARNING)
                raise ValueError(f"配置文件必要配置项缺失，其中[{key}]是缺失的")
            else:
                unique_constraints[key] = ''

        return unique_constraints
    def load_import_config(self, file_path: str) -> bool:
        """仅仅加载配置文件到配置gui列表上,但不使用对应的配置文件
        Args:
            file_path: 配置文件绝对路径

        需要在SERVER_CONFIG_UNIQUE_KEYS-USER_CUSTOM_KEYS的范围内校验多进程唯一性
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        logger_print(f"attempting to load import config---{file_path}", custom_logger=logger)

        if not file_path or not isinstance(file_path, str):
            logger_print("file_path must be a non-empty string", custom_logger=logger, log_level=logging.ERROR)
            raise ValueError("需要加载的配置文件路径不能为空")

        # 转换为绝对路径
        abs_file_path = get_real_path(file_path)

        def __load_import_with_cleanup(cur: sqlite3.Cursor):
            logger_print(msg="executing cleanup and validation before loading config", custom_logger=logger, log_level=logging.DEBUG)
            # 配置文件有效性合唯一配置项校验
            config_check.check_new_get_config_file(server_config_file_path=abs_file_path, cur=cur)

            logger_print(msg="calculating file hash and extracting config items", custom_logger=logger, log_level=logging.DEBUG)
            # 计算文件哈希
            checksum = file_hash(abs_file_path)
            # 提取唯一性的配置项
            unique_constraints = MultiProcessConfigManager._extract_unique_constraints_from_config_file(abs_file_path)
            config_name=unique_constraints['app_name']
            # 插入配置记录
            insert_config_record_sql="INSERT INTO config_file_record (name, file_path, checksum, last_used) VALUES (?, ?, ?, CURRENT_TIMESTAMP);"
            cur.execute(insert_config_record_sql, (config_name, abs_file_path, checksum))
            config_record_id = cur.lastrowid
            # 插入配置项约束记录
            insert_unique_constraints_sql="INSERT INTO config_unique_constraints (config_file_record_id, config_key, config_value) VALUES (?, ?, ?);"
            for config_key, config_value in unique_constraints.items():
                cur.execute(insert_unique_constraints_sql, (config_record_id, config_key, config_value))

            logger_print(msg=f"successfully loaded import config: {config_name} at {abs_file_path}", custom_logger=  logger)
            return True

        try:
            return self._exec_retry(__load_import_with_cleanup)
        except Exception as failed_register_ex: # noqa
            logger_print(msg=f"error loading config: {abs_file_path}", custom_logger=logger, use_exception=True, exception=failed_register_ex)
            # 给前端提示
            raise ValueError(f"不能使用{abs_file_path}配置文件,原因为:{str(failed_register_ex)}")

    def register_config_after_check(self,server_config_file_path: str,unique_constraints_dict:dict[str,str],cur: sqlite3.Cursor):
        """
        在配置文件新注册前已经完成校验,这里只需要进行数据库记录的新增操作
        Args:
            server_config_file_path: 配置文件绝对路径
            unique_constraints_dict: 配置项唯一约束字典
            cur: sqlite3.Cursor

        Returns:
            无返回值,只进行数据库插入操作,默认操作成功
        """
        checksum = file_hash(server_config_file_path)

        config_name=unique_constraints_dict['app_name']
        logger_print(msg=f"inserting config record for: {config_name}", custom_logger=logger, log_level=logging.DEBUG)
        # 插入配置记录
        insert_config_record_sql="INSERT INTO config_file_record (name, file_path, process_id, checksum, last_used) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP);"
        cur.execute(insert_config_record_sql, (config_name, server_config_file_path, self._pid, checksum))
        config_record_id = cur.lastrowid
        logger_print(msg="inserting config unique constraints", custom_logger=logger, log_level=logging.DEBUG)
        # 插入配置项约束记录
        insert_unique_constraints_sql="INSERT INTO config_unique_constraints (config_file_record_id, config_key, config_value) VALUES (?, ?, ?);"
        for config_key, config_value in unique_constraints_dict.items():
            cur.execute(insert_unique_constraints_sql, (config_record_id, config_key, config_value))

        logger_print(msg="updating local state", custom_logger=logger, log_level=logging.DEBUG)
        # 更新本地状态
        with self._config_lock:
            self._registered_config_path = server_config_file_path
            self._registered_config_id = config_record_id
            self._is_registered = True

        # 启动心跳线程
        self._start_heartbeat_thread()

        logger_print(msg=f"successfully registered config: {config_name} at {server_config_file_path}", custom_logger=logger)
    def load_db_config_to_process(self, record:dict[str,str], cur: sqlite3.Cursor):
        """
        当前进程使用配置文件[非配置加载界面]时,从数据库中加载配置到进程,说明该配置文件被当前进程占用
        :param cur:
        :param record: 数据库查询到的配置记录
        :return: 只需要不报错说明操作成功
        """
        server_config_path=record['file_path']
        process_id=record['process_id']
        is_active=int(record['is_active'])
        fail_reason=record['fail_reason']
        checksum=record['checksum']
        server_config_id=int(record['id'])
        if process_id :
            if process_id == self._pid:
                return
            # 该配置文件已被占用,该配置文件已被其他进程占用
            logger_print(msg=f"config file {server_config_path} has already registered in other process: {process_id}", custom_logger=logger, log_level=logging.ERROR)
            raise ValueError(f"配置文件{server_config_path}已被其他进程:[{process_id}]占用!")
        elif is_active == 0:
            # 该配置文件已失效,前面已经判断配置文件是否有效,所以说如果is_active为0,说明该配置文件已失效
            logger_print(msg=f"config file {server_config_path} has already been marked as invalid", custom_logger=logger, log_level=logging.ERROR)
            raise ValueError(f"配置文件{server_config_path}已失效,失效原因是:{fail_reason}!")
        else:
            logger_print(msg="validating config file integrity", custom_logger=logger, log_level=logging.DEBUG)
            self._check_one_record_valid(cur,server_config_id, server_config_path, checksum)
            logger_print(msg="updating config record with current process", custom_logger=logger, log_level=logging.DEBUG)
            set_pid_sql="UPDATE config_file_record SET process_id = ?, updated_at = CURRENT_TIMESTAMP, last_used = CURRENT_TIMESTAMP WHERE id = ? ;"
            cur.execute(set_pid_sql, (self._pid, server_config_id))
            # 更新本地状态
            with self._config_lock:
                self._registered_config_path = server_config_path
                self._registered_config_id = server_config_id
                self._is_registered = True

            # 启动心跳线程
            self._start_heartbeat_thread()
            logger_print(msg=f"successfully loaded config: {server_config_path}", custom_logger=logger)

    def register_config(self, file_path: str) -> bool:
        """注册并使用配置文件
        Args:
            file_path: 配置文件绝对路径

        需要在SERVER_CONFIG_UNIQUE_KEYS-USER_CUSTOM_KEYS的范围内校验多进程唯一性
        Returns:
            bool: 注册成功返回True，失败返回False
        """
        logger_print(msg=f"attempting to register config---{file_path}", custom_logger=logger)

        if not file_path or not isinstance(file_path, str):
            # 替换logger.error
            logger_print(msg="file_path must be a non-empty string", custom_logger=logger, log_level=logging.ERROR)
            raise ValueError("注册的配置文件路径不能为空")

        # 检查当前进程是否已注册配置
        with self._config_lock:
            if self._is_registered:
                logger_print(msg=f"process {self._pid} already registered a config", custom_logger=logger, log_level=logging.ERROR)
                return False

        # 转换为绝对路径
        abs_file_path = get_real_path(file_path)

        def _register_with_cleanup(cur: sqlite3.Cursor):
            logger_print(msg="executing cleanup and validation before registering config", custom_logger=logger, log_level=logging.DEBUG)
            # 配置文件有效性校验
            config_check.check_new_get_config_file(server_config_file_path=abs_file_path, cur=cur)
            logger_print(msg="calculating file hash and extracting config items", custom_logger=logger, log_level=logging.DEBUG)
            # 提取唯一性的配置项
            unique_constraints = MultiProcessConfigManager._extract_unique_constraints_from_config_file(abs_file_path)
            self.register_config_after_check(abs_file_path,unique_constraints,cur)
            return True

        try:
            return self._exec_retry(_register_with_cleanup)
        except Exception as failed_register_ex: # noqa
            logger_print(msg=f"error registering config: {abs_file_path}", custom_logger=logger, use_exception=True, exception=failed_register_ex)
            # 给前端提示
            raise ValueError(f"不能使用{abs_file_path}配置文件,原因为:{str(failed_register_ex)}")

    def _check_one_registered_record_valid(self, cur: sqlite3.Cursor, server_config_path: str):
        """
        校验单条已经注册配置记录现在是否有效:
        配置文件是否被外部程序修改
        配置文件配置项是否符合格式要求
        :args:
            cur: sqlite3.Cursor
            server_config_path: 配置文件绝对路径 必须是配置文件的绝对路径
        """
        get_checksum_sql="SELECT id,checksum FROM config_file_record WHERE file_path = ? ;"
        cur.execute(get_checksum_sql, (server_config_path,))
        server_config_id,stored_checksum = cur.fetchone()
        self._check_one_record_valid(cur,server_config_id, server_config_path, stored_checksum)

    def check_one_registered_record_valid(self, server_config_path: str):
        """
        校验单条已经注册配置记录现在是否有效:
        配置文件是否被外部程序修改
        配置文件配置项是否符合格式要求
        """
        def check(cur: sqlite3.Cursor):
            self._check_one_registered_record_valid(cur,  server_config_path)
        return self._exec_retry(check)


    def update_config(self, section:str, config_key_values:dict[str,str]) -> bool:
        """更新配置文件项,一般使用在用户界面更新配置项
        注意:如果其是唯一配置项,则对应数据库项也会修改
        Args:
            section: 配置项所属节点
            config_key_values: 配置项键-值对 至少一个配置项
        Returns:
            bool: 更新成功返回True，失败返回False
        """
        if not config_key_values or not isinstance(config_key_values, dict):
            logger_print("config_key_values must be a non-empty dict", custom_logger=logger, log_level=logging.ERROR)
            raise ValueError("新增或者修改的配置项不能为空")
        with self._config_lock:
            if not self._is_registered:
                logger_print(f"server config not registered in this process,cannot update", custom_logger=logger, log_level=logging.WARNING)
                raise ValueError("当前进程未注册配置文件,请先注册配置文件后再更新")

        def _update_with_cleanup(cur: sqlite3.Cursor):
            logger_print(msg="executing cleanup and validation before updating config", custom_logger=logger, log_level=logging.DEBUG)
            # 比较哈希值新值和旧值是否相同:避免被外部程序修改
            self._check_one_registered_record_valid(cur, self._registered_config_path)
            # 如果该配置项是唯一项,需要校验其唯一性
            for key, value in config_key_values.items():
                # value是不能为空的,校验输入的配置项key-value是否符合格式要求
                config_check.check_config_section_key_value_format_valid(section, key, value)
                if section =='server' and key in gui_constants.SERVER_CONFIG_UNIQUE_KEYS:
                    check_exist_unique_constraint_sql="""SELECT COUNT(*) FROM config_unique_constraints cuc
                        JOIN config_file_record cfr ON cuc.config_file_record_id = cfr.id
                        WHERE cuc.config_key = ? AND cuc.config_value = ? and cuc.config_file_record_id != ? ;"""
                    cur.execute(check_exist_unique_constraint_sql, (key, value,self._registered_config_id))

                    if cur.fetchone()[0] != 0:
                        logger_print(msg=f"config item conflict before update: {key}={value}", custom_logger=logger, log_level=logging.ERROR)
                        raise ValueError(f"当前{key}配置项值{value}已被其他配置使用,请更换新的值后再尝试更新")
                    # 更新数据库
                    update_unique_constraint_value_sql="UPDATE config_unique_constraints SET config_value = ? ,updated_at = CURRENT_TIMESTAMP WHERE config_file_record_id = ? AND config_key = ? ;"
                    cur.execute(update_unique_constraint_value_sql, (value, self._registered_config_id, key))
            # 更新实际的文件内容
            if section=='client_info':
                # 由于client_info节点下的所有配置项是需要完全同步的,所以需要进行配置文件的完全同步
                synch_section(self._registered_config_path, section, config_key_values)
            else:
                # 其他节点的配置项只需要同步指定配置项key对应的配置项,管不到全部的配置项
                 update_config(self._registered_config_path, section, config_key_values)
            # 更新配置记录
            new_checksum_calc= file_hash(self._registered_config_path)
            update_record_sql="UPDATE config_file_record SET checksum = ?, updated_at = CURRENT_TIMESTAMP, last_used = CURRENT_TIMESTAMP WHERE id = ? ;"
            cur.execute(update_record_sql, (new_checksum_calc, self._registered_config_id))
            logger_print(msg=f"successfully updated config: {self._registered_config_path}", custom_logger=logger)
            return True

        try:
            return self._exec_retry(_update_with_cleanup)
        except Exception as error_update_ex:
            logger_print(msg="error updating config", custom_logger=logger, use_exception=True, exception=error_update_ex)
            raise ValueError(f"更新配置文件{self._registered_config_path}时发生错误\n错误原因:{str(error_update_ex)}")

    def unregister_config(self) -> bool:
        """注销当前进程的配置:只能注销自己的配置,不能注销其他进程的配置
        配置文件不被当前进程占用,配置文件记录本身不删除
        Returns:
            bool: 注销成功返回True，失败返回False
        """
        logger_print(msg=f"{self.__class__.__name__} attempting to unregister config", custom_logger=logger)
        with self._config_lock:
            if not self._is_registered:
                logger_print(msg="this process has not registered a config, cannot unregister!", custom_logger=logger, log_level=logging.WARNING)
                return False
        def _unregister(cur: sqlite3.Cursor):
            logger_print(msg=f"{self.__class__.__name__} attempting to unregister config: {self._registered_config_path} when run sql", custom_logger=logger)
            # 释放配置占用
            set_no_pid_sql="UPDATE config_file_record SET process_id = NULL, last_used = CURRENT_TIMESTAMP,updated_at = CURRENT_TIMESTAMP WHERE id = ? ;"
            cur.execute(set_no_pid_sql, (self._registered_config_id,))

            if cur.rowcount != 1:
                logger_print(msg=f"failed to unregister config: {self._registered_config_path}", custom_logger=logger, log_level=logging.ERROR)
                return False

            # 更新本地状态
            with self._config_lock:
                self._registered_config_path = None
                self._registered_config_id = None
                self._is_registered = False

            # 停止心跳线程
            self._stop_heartbeat_thread()

            logger_print(msg=f"{self.__class__.__name__} successfully unregistered config: {self._registered_config_path}", custom_logger=logger)
            return True

        try:
            return self._exec_retry(_unregister)
        except Exception:# noqa
            logger_print(f"error unregistering config!", custom_logger=logger, use_exception=True)
            return False

    def register_config_only_command(self, file_path: str):
        """
        仅仅在命令行模式下使用时进行注册并使用配置文件
        如果是由gui界面启动的服务端则不能使用该函数
        Args:
            file_path: 配置文件绝对路径

        需要在SERVER_CONFIG_UNIQUE_KEYS的范围内校验多进程唯一性
        由于其已经检查了配置文件的格式正确这里就不需要进行而外的检查,仅仅需要保存唯一性配置项插入数据库即可
        不需要返回值,如果出错则会抛出异常
        """
        logger_print(msg=f"attempting to register config---{file_path}", custom_logger=logger)
        with self._config_lock:
            if  self._is_registered:
                if self._registered_config_path!= file_path:
                    logger_print(msg="server config has already registered in this process,please select other config file to load!", custom_logger=logger, log_level=logging.WARNING)
                    raise ValueError("当前进程已经注册配置文件,不能再次加载新的配置文件")
                else:
                    logger_print(msg="server config has already registered in this process,skip loading new config", custom_logger=logger)
                    return

        # 不需要转换为绝对路径,在webhook_server中已经进行了转换

        def _register_config_only_command(cur: sqlite3.Cursor):
            logger_print(msg="executing cleanup and validation before registering config on command line mode", custom_logger=logger, log_level=logging.DEBUG)
            # 1. 判断该配置文件是否已经注册了
            get_record_by_path_sql="SELECT process_id,is_active,fail_reason,checksum,id,file_path  FROM config_file_record WHERE file_path = ? ;"
            cur.execute(get_record_by_path_sql, (file_path,))
            record = cur.fetchone()
            if  record:
                # 更新配置占用记录
                record_dict=dict(zip([desc[0] for desc in cur.description],record))
                self.load_db_config_to_process(record_dict, cur)
                return
            # 新注册
            # 只需要校验唯一性配置项
            unique_constraints = MultiProcessConfigManager._extract_unique_constraints_from_config_file(file_path)
            # 由于调用该函数是后台服务端 webhook_server,其已经检查了配置文件的格式正确,所以对应的配置项的key值肯定存在且配置项完整不区分是否是gui自定义配置项
            for unique_key,cur_value in unique_constraints.items():
                if MultiProcessConfigManager.check_value_exist_in_db(unique_key, cur_value, cur):
                    raise ValueError(f"服务端配置文件{file_path}的唯一性配置项{unique_key}的值:{cur_value}已经存在于数据库中,为保证唯一性,请修改该值!")
            logger_print(msg="calculating file hash and extracting config items", custom_logger=logger, log_level=logging.DEBUG)
            self.register_config_after_check(file_path,unique_constraints,cur)

        try:
            return self._exec_retry(_register_config_only_command)
        except Exception as failed_register_ex: # noqa
            logger_print(msg=f"error registering config: {file_path}", custom_logger=logger, use_exception=True, exception=failed_register_ex)
            # 给前端提示
            raise ValueError(f"不能使用{file_path}配置文件,原因为:{str(failed_register_ex)}")
    def main_gui_load_config(self,server_config_path:str):
        """加载配置文件到主GUI进程"""
        if not server_config_path or not isinstance(server_config_path, str):
            logger_print("file_path must be a non-empty string", custom_logger=logger, log_level=logging.ERROR)
            raise ValueError("注册的配置文件路径不能为空")

        # 转换为绝对路径
        abs_file_path = get_real_path(server_config_path)
        with self._config_lock:
            if  self._is_registered:
                if self._registered_config_path!= abs_file_path:
                    logger_print("server config has already registered in this process,please select other config file to load!", custom_logger=logger, log_level=logging.WARNING)
                    raise ValueError("当前进程已经注册配置文件,不能再次加载新的配置文件")
                else:
                    logger_print("server config has already registered in this process,skip loading new config", custom_logger=logger)
                    return
        def _main_gui_load_config(cur: sqlite3.Cursor):
            logger_print(msg="executing cleanup and validation before registering config on main gui mode", custom_logger=logger, log_level=logging.DEBUG)
            # 1. 判断该配置文件是否已经注册了
            get_record_by_path_sql="SELECT process_id,is_active,fail_reason,checksum,id,file_path  FROM config_file_record WHERE file_path = ? ;"
            cur.execute(get_record_by_path_sql, (abs_file_path,))
            record = cur.fetchone()
            if  record:
                # 更新配置占用记录
                record_dict=dict(zip([desc[0] for desc in cur.description],record))
                self.load_db_config_to_process(record_dict, cur)
                return
            # 新注册
            # 配置文件有效性校验
            config_check.check_new_get_config_file(server_config_file_path=abs_file_path, cur=cur)
            logger_print(msg="calculating file hash and extracting config items", custom_logger=logger, log_level=logging.DEBUG)
            # 提取唯一性的配置项
            unique_constraints = MultiProcessConfigManager._extract_unique_constraints_from_config_file(abs_file_path)
            self.register_config_after_check(abs_file_path,unique_constraints,cur)

        try:
            self._exec_retry(_main_gui_load_config)
        except (ValueError, FileNotFoundError, PermissionError) as config_error:
            logger_print(msg=f"configuration error loading config: {abs_file_path} - {config_error}", custom_logger=logger, log_level=logging.ERROR)
            raise
        except sqlite3.Error as db_error:
            logger_print(msg=f"database error loading config: {abs_file_path} - {db_error}", custom_logger=logger, log_level=logging.ERROR)
            raise
        except Exception as unexpected_error:
            logger_print(msg=f"unexpected error loading config: {abs_file_path} - {unexpected_error}", custom_logger=logger, log_level=logging.ERROR)
            raise

    def get_valid_values_for_key(self, config_key: str) -> set[str]:
        """获取指定配置键的所有有效值"""
        def _query(cur: sqlite3.Cursor):
            get_valid_values_sql="""
            SELECT cuc.config_value
            FROM config_unique_constraints cuc JOIN config_file_record cfr ON cuc.config_file_record_id = cfr.id
            WHERE cfr.is_active = 1 AND cuc.config_key = ? ;
            """
            cur.execute(get_valid_values_sql, (config_key,))
            return sqlite_base_manager.SQLiteBaseManager.get_field_datas(rows=cur.fetchall(), field_index=0, need_only=True)

        try:
            return self._exec_retry(_query)
        except sqlite3.Error as db_error:
            logger_print(f"database error getting valid values for key {config_key}: {db_error}", custom_logger=logger, log_level=logging.ERROR)
            raise
        except Exception as unexpected_error:
            logger_print(f"unexpected error getting valid values for key {config_key}: {unexpected_error}", custom_logger=logger, log_level=logging.ERROR)
            raise
    def get_config_path_in_db(self, server_config_path:str) -> dict[str,str]|None:
        """检查数据库中是否存在指定路径的配置文件记录"""
        def query_data(cur: sqlite3.Cursor):
            get_record_by_path_sql="SELECT process_id,is_active,fail_reason,checksum,id,file_path  FROM config_file_record WHERE file_path = ? ;"
            cur.execute(get_record_by_path_sql, (server_config_path,))
            record = cur.fetchone()
            return dict(zip([desc[0] for desc in cur.description], record)) if record else None
        return self._exec_retry(query_data)
    @staticmethod
    def check_config_path_in_db(server_config_path:str,cur: sqlite3.Cursor) -> bool:
        """检查数据库中是否存在指定路径的配置文件记录"""
        record_by_path_count_sql = "SELECT COUNT(*)  FROM config_file_record WHERE file_path = ? ;"
        cur.execute(record_by_path_count_sql, (server_config_path,))
        return cur.fetchone()[0] > 0
    def check_config_in_db(self) -> bool:
        """检查数据库中是否存在配置文件记录:配置加载界面中首次加载判断"""
        all_record_count_sql = "SELECT COUNT(*)  FROM config_file_record;"
        return self._exec_retry(lambda cur: cur.execute(all_record_count_sql).fetchone()[0])>0
    def check_occupied_config_in_db(self) -> bool:
        """检查数据库中是否存在正在使用配置文件记录的程序:配置加载界面打开时判断是否需要在存活一个程序的情况下打开第二个程序"""
        all_occupied_record_count_sql = "SELECT COUNT(*)  FROM config_file_record where process_id is not null ;"
        return self._exec_retry(lambda cur: cur.execute(all_occupied_record_count_sql).fetchone()[0])>0
    @staticmethod
    def check_value_exist_in_db(config_key:str,config_value:str,cur: sqlite3.Cursor)->bool:
        """检查数据库中是否存在指定配置项:一般使用在注册前或手动加载配置文件前"""
        get_unique_constraints_count_sql = "SELECT COUNT(*)  FROM config_unique_constraints WHERE config_key = ? AND config_value = ? ;"
        cur.execute(get_unique_constraints_count_sql, (config_key,config_value))
        return cur.fetchone()[0] > 0
    def get_all_server_config_paths_in_db(self) -> set[str]:
        """获取数据库中所有服务器配置路径[无论当前记录中其配置是否有效]"""
        def _query(cur: sqlite3.Cursor):
            get_all_record_file_path_sql="SELECT file_path FROM config_file_record;"
            cur.execute(get_all_record_file_path_sql)
            return sqlite_base_manager.SQLiteBaseManager.get_field_datas(rows=cur.fetchall(), field_index=0, need_only=True)
        try:
            return self._exec_retry(_query)
        except Exception: # noqa
            logger_print(f"error getting all server config paths!", custom_logger=logger, log_level=logging.ERROR)
            raise

    def check_config_registrable(self, file_path: str) -> Tuple[bool, Optional[str]]:
        """检查配置文件是否可以注册

        Args:
            file_path: 配置文件路径

        Returns:
            (can_register, error_reason): 是否可以注册和错误原因
        """
        if not file_path or not isinstance(file_path, str):
            return False, "配置文件路径不能为空"
        abs_file_path = get_real_path(file_path)
        def wrapper_function(cursor: sqlite3.Cursor):
            config_check.check_new_get_config_file(server_config_file_path=abs_file_path, cur=cursor)
        try:
            # 配置文件有效性校验
            self._exec_retry(wrapper_function)
            return True, None
        except Exception as register_ex:
            logger_print(msg="error checking config registrable", custom_logger=logger, use_exception=True, exception=register_ex)
            return False, f"该配置不可用!原因是: {str(register_ex)}"

    def delete_invalid_configs(self)->int:
        """删除所有无效配置记录 --- 清理无效按钮"""
        def _delete(cur: sqlite3.Cursor)->int:
            # 删除无效的配置项约束记录
            delete_invalid_constraints_sql="DELETE FROM config_unique_constraints WHERE config_file_record_id NOT IN (SELECT id FROM config_file_record);"
            cur.execute(delete_invalid_constraints_sql)
            # 清理失效的配置项约束记录
            delete_inactive_constraints_sql="DELETE FROM config_unique_constraints WHERE config_file_record_id IN (SELECT id FROM config_file_record WHERE is_active = 0);"
            cur.execute(delete_inactive_constraints_sql)
            # 删除失效的配置记录
            delete_inactive_records_sql="DELETE FROM config_file_record WHERE is_active = 0;"
            cur.execute(delete_inactive_records_sql)
            cur_deleted_count = cur.rowcount
            # 替换logger.info
            logger_print(msg=f"deleted {cur.rowcount} invalid config records", custom_logger=logger)
            return cur_deleted_count

        try:
            return self._exec_retry(_delete)
        except Exception as deleted_ex:
            # 替换logger.exception
            logger_print(msg=f"error deleting invalid configs!", custom_logger=logger, use_exception=True, exception=deleted_ex)
            raise ValueError(f"删除无效配置失败!原因是: {str(deleted_ex)}")

    def _cleanup_current_process(self):
        """当前进程结束时清理相关资源"""
        try:
            with self._config_lock:
                if not self._is_registered:
                    return

                logger_print(msg=f"{self.__class__.__name__} cleaning up current process resources", custom_logger=logger)

                # 停止心跳线程
                self._stop_heartbeat_thread()

                # 注销配置
                if self._registered_config_path:
                    self.unregister_config()

                logger_print(msg=f"{self.__class__.__name__} cleanup completed", custom_logger=logger)

        except Exception: # noqa
            logger_print(msg=f"{self.__class__.__name__} cleanup failed", custom_logger=logger, use_exception=True)


    def get_latest_config_records(self)->Tuple[List[Dict],List[Dict],List[Dict]]:
        """获取最新的配置记录:gui界面用"""
        def _refresh(cur: sqlite3.Cursor):
            return self._get_all_config_records_to_by_status(cur)
        # 不需要异常处理,如果出现了异常直接抛出前端界面显示异常原因
        self._release_validate_cleanup_invalid_records()
        return self._exec_retry(_refresh)
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self._cleanup_current_process()
        except Exception:  # noqa
            pass  # 忽略析构时的异常
