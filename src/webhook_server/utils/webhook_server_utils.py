# 检查输入的单个发信设备信息是否合法
import logging
import os
import subprocess
import sys
import tempfile
from typing import Optional

from common.constants import common_constants
from common.utils.logging_utils import logger_print
from common.utils.path_utils import get_real_path_create_dir
from common.utils.system_utils import is_pyinstaller_bundle
from webhook_server.config import constants, gui_constants

logger = logging.getLogger(__name__)

#校验填入输入框中的发信设备信息是否合法【发信设备标识+描述】
def check_one_client_info(client_key: str|None, client_desc: str|None) -> bool:
    if not isinstance(client_key, str) or not isinstance(client_desc, str):
        return False
    return (constants.MIN_CLIENT_KEY_LEN <= len(
        client_key) <= constants.MAX_CLIENT_KEY_LEN) and constants.MIN_CLIENT_DESC_LEN < len(
        client_desc) and constants.CLIENT_KEY_PATTERN.fullmatch(client_key)


# 检查所有发信设备标识配置信息是否合法
def check_all_client_info_config(client_info_config: dict):
    for client_key, client_desc in client_info_config.items():
        if not check_one_client_info(client_key, client_desc):
            raise ValueError(constants.ERROR_CLIENT_INPUT_MSG)

def write_error_to_temp_file(error_message: str):
    """将错误信息写入临时文件，供父进程读取"""
    try:
        # 创建错误文件，直接使用进程PID作为文件名
        error_dir = get_real_path_create_dir(
            path=gui_constants.ERROR_SERVER_GUI_DIR,
            path_is_dir=True
        )
        error_file_path = os.path.join(error_dir, f"gui_error_{os.getpid()}.txt")

        # 直接写入错误信息
        with open(error_file_path, 'w', encoding='utf-8') as f:
            f.write(error_message)

        logger_print(
            msg=f"error information written to file: {error_file_path}",
            custom_logger=logger,log_level=logging.ERROR
        )

        # 确保文件写入完成
        os.sync() if hasattr(os, 'sync') else None
    except Exception as write_error:
        logger_print(
            msg=f"failed to write error to file: {write_error}",
            custom_logger=logger,
            use_exception=True
        )

def read_error_file_by_pid( child_pid: int) -> Optional[str]:
    """根据子进程PID直接读取错误文件"""
    try:
        # 直接构造错误文件路径
        error_file_path = os.path.join(gui_constants.ERROR_SERVER_GUI_DIR, f"gui_error_{child_pid}.txt")
        if os.path.exists(error_file_path):
            # 直接读取错误信息
            with open(error_file_path, 'r', encoding='utf-8') as f:
                error_message = f.read().strip()
            # 清理错误文件（父进程读取后删除）
            try:
                os.remove(error_file_path)
            except Exception as cleanup_error:
                logger_print(
                    msg=f"failed to cleanup error file {error_file_path}: {cleanup_error}",
                    custom_logger=logger,
                    use_exception=True
                )
            return error_message
    except Exception as e:
        logger_print(
            msg=f"error reading error file for pid {child_pid}: {e}",
            custom_logger=logger,
            use_exception=True
        )

    return None


def start_independent_process(config_path: str,argument_param:str,relative_script_path:str) -> subprocess.Popen:
    """
    启动和配置文件相关的独立进程，支持打包和非打包环境
    :param config_path: 配置文件路径
    :param argument_param: 在exe环境下的启动参数
    :param relative_script_path: 在非exe环境下的该进程的对于该项目的相对脚本路径
    :return: 新进程对象
    """
    is_pyinstaller=is_pyinstaller_bundle()
    logger_print(msg=f"starting independent process with config_path: {config_path},argument_param: {argument_param},relative_script_path: {relative_script_path}", custom_logger=logger)
    cur_env = os.environ.copy()
    if is_pyinstaller and not common_constants.IS_WINDOWS:
        child_temp = tempfile.mkdtemp(prefix="gui_")
        cur_env.update(TMP=child_temp, TEMP=child_temp, TMPDIR=child_temp)

    if is_pyinstaller:
        # 构造启动命令：直接调用 exe 本身（sys.executable 就是打包后的 .exe）
        cmd = [sys.executable, argument_param, config_path]
        # PyInstaller 打包环境：直接启动当前 exe 文件，传递特殊参数
        logger_print(msg="detected pyinstaller bundle environment, using exe launch method", custom_logger=logger)
        if common_constants.IS_WINDOWS:
            # Windows 平台：创建独立进程，不捕获stderr避免文件描述符问题
            process = subprocess.Popen(cmd,creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NEW_PROCESS_GROUP,env=cur_env)
        else:
            # Unix-like 平台：设置新的会话组
            # 确保DISPLAY环境变量设置正确
            cur_env.setdefault('DISPLAY', ':0.0')
            process = subprocess.Popen(cmd,preexec_fn=os.setsid,env=cur_env)
    else:
        # 开发环境：使用直接启动方式
        gui_script_path = common_constants.get_resource_path(relative_script_path)
        print(f"xxx:{gui_script_path}")
        logger_print(msg=f"detected development environment, using direct launch method with gui_script_path: {gui_script_path}", custom_logger=logger)
        if common_constants.IS_WINDOWS:
            # Windows 平台：创建独立进程，隐藏控制台窗口
            process = subprocess.Popen(
                [sys.executable, gui_script_path, "--config", config_path],
                creationflags=subprocess.DETACHED_PROCESS |subprocess.CREATE_NEW_PROCESS_GROUP,env=cur_env
            )
        else:
            # Unix-like 平台（Linux/macOS）
            # 确保DISPLAY环境变量设置正确
            cur_env.setdefault('DISPLAY', ':0.0')
            process = subprocess.Popen(
                [sys.executable, gui_script_path, "--config", config_path],
                preexec_fn=os.setsid,
                env=cur_env
            )

    logger_print(msg=f"independent process started with pid: {process.pid}", custom_logger=logger)
    return process
