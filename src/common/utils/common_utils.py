"""通用工具模块。

此模块提供了通用的工具函数，包括：
- 字符串处理和转换
- 函数执行控制
- 数据类型转换
- 存储单位转换
- 字典操作工具
"""

import inspect
import logging
import threading
from typing import Any, Callable, Dict, Set, Tuple

from common.constants import common_constants

logger = logging.getLogger(__name__)

# 全局注册表，保存每个 func 对应的执行状态和锁
_run_once_locks: dict[Callable[..., Any], threading.Lock] = {}
_run_once_flags: dict[Callable[..., Any], bool] = {}
_run_once_global_lock = threading.Lock()


def trim(str_value: str | None) -> str | None:
    """该函数用于将字符串中前后空格去除,为空则返回None
    
    Args:
        str_value: 输入字符串
        
    Returns:
        str | None: 去除空格后的字符串，空字符串返回None
    """
    from common.utils.logging_utils import logger_print
    
    if str_value is None:
        return None
    if not isinstance(str_value, str):
        logger_print(f"current value is not string, current value is {str_value}, type is {type(str_value)}", custom_logger=logger, log_level=logging.WARNING)
        return None
    strip_value = str_value.strip()
    return strip_value or None


def str_to_bool(s: str) -> bool | None:
    """该函数用于将字符串转换为布尔值,如果字符串为空则返回None
    
    Args:
        s: 输入字符串
        
    Returns:
        bool | None: 转换后的布尔值
        
    Raises:
        ValueError: 字符串格式无效
    """
    s = trim(s)
    if s is None:
        return None
    if s.lower() == 'true':
        return True
    elif s.lower() == 'false':
        return False
    else:
        raise ValueError(f"invalid bool string: {s},the string must be 'true' or 'false'")


def run_once(func: Callable[..., Any], *args, **kwargs) -> Any:
    """该函数用于保证函数只执行一次且线程安全[使用该函数越多,对应的func也需要锁和状态描述,造成内存会越来越大:绑定在类实例上]
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        **kwargs: 函数关键字参数
        
    Returns:
        Any: 函数执行结果
    """
    from common.utils.logging_utils import logger_print
    
    func_name = getattr(func, '__name__', str(func))
    logger_print(f"run_once called for function: {func_name}", custom_logger=logger)

    with _run_once_global_lock:
        if func not in _run_once_locks:
            logger_print(f"initializing lock and flag for function: {func_name}", custom_logger=logger)
            _run_once_locks[func] = threading.Lock()
            _run_once_flags[func] = False
        else:
            logger_print(f"using existing lock for function: {func_name}", custom_logger=logger)

    lock = _run_once_locks[func]
    with lock:
        if not _run_once_flags[func]:
            logger_print(f"executing function {func_name} for the first time", custom_logger=logger)
            _run_once_flags[func] = True
            result = func(*args, **kwargs)
            logger_print(f"function {func_name} executed successfully", custom_logger=logger)
            return result
        else:
            logger_print(f"function {func_name} already executed, skipping", custom_logger=logger)


def convert_storage_str_to_bytes(storage_str: str) -> float:
    """转换存储单位为字节数,storage_str 如 "1.25MB"
    
    Args:
        storage_str: 存储单位字符串
        
    Returns:
        float: 字节数
        
    Raises:
        ValueError: 存储字符串格式无效
    """
    storage_str = trim(storage_str)
    if storage_str is None:
        raise ValueError("storage string can not be empty!")
    storage_str = storage_str.replace(" ", "")
    str_match = common_constants.STORAGE_UNIT_PATTERN.fullmatch(storage_str)
    if not str_match:
        raise ValueError(f"invalid storage string: {storage_str}")
    num, unit = str_match.groups()
    num = float(num)
    unit = unit.upper()
    try:
        multiplier = common_constants.STORAGE_UNIT[unit]
    except KeyError:
        raise ValueError(f"unknown unit: {unit!r} in {storage_str!r}")

    return float(num * multiplier)


def get_miss_key_in_dict(dict_obj: dict[str, Any], required_keys: set[str]) -> set[str]:
    """获取相对于required_keys来说，字典中缺少的键,包含dict中value为None的键也视为缺少

    Args:
        dict_obj: 字典对象
        required_keys: 必需的键集合

    Returns:
        set[str]: 缺少的键集合
    """
    missing_keys = set()
    for key in required_keys:
        if key not in dict_obj or dict_obj[key] is None:
            missing_keys.add(key)
    return missing_keys


def is_same_function(
    func1: Callable[..., Any], args1: Tuple[Any, ...], kwargs1: Dict[str, Any],
    func2: Callable[..., Any], args2: Tuple[Any, ...], kwargs2: Dict[str, Any],
) -> bool:
    """Compare two "function" (function + args/kwargs) for semantic equality.
    - Unwraps bound methods so that `obj.method` is tested by (function, instance).
    - Normalizes defaults via inspect.signature.bind_partial + apply_defaults.

    Args:
        func1: 第一个函数
        args1: 第一个函数的位置参数
        kwargs1: 第一个函数的关键字参数
        func2: 第二个函数
        args2: 第二个函数的位置参数
        kwargs2: 第二个函数的关键字参数

    Returns:
        bool: 两个函数调用是否语义相等
    """

    def unwrap(f: Callable) -> Tuple[Callable, Any]:
        """If f is a bound method, return (f.__func__, f.__self__), else (f, None)."""
        # Unwrap decorator chain
        while hasattr(f, '__wrapped__'):
            f = f.__wrapped__
        if hasattr(f, "__self__") and hasattr(f, "__func__"):
            return f.__func__, f.__self__
        return f, None

    def normalize(f: Callable, args: Tuple[Any, ...], kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Bind given args/kwargs to the signature of f, fill in defaults, and return the full argument mapping."""
        sig = inspect.signature(f)
        try:
            bound = sig.bind(*args, **kwargs)  # 改为全绑定方式
        except TypeError:
            bound = sig.bind_partial(*args, **kwargs)
        bound.apply_defaults()
        # 处理可变参数
        arguments = dict(bound.arguments)
        for param in sig.parameters.values():
            if param.kind == param.VAR_POSITIONAL:
                arguments[param.name] = tuple(arguments.get(param.name, []))
            elif param.kind == param.VAR_KEYWORD:
                arguments[param.name] = dict(arguments.get(param.name, {}))

        return arguments

    # 1) Unwrap bound methods
    real_func1, self1 = unwrap(func1)
    real_func2, self2 = unwrap(func2)
    if real_func1 is not real_func2 or self1 != self2:
        return False

    # 2) Normalize argument binding
    try:
        norm1 = normalize(real_func1, args1, kwargs1)
        norm2 = normalize(real_func2, args2, kwargs2)
    except TypeError:
        # If they can't even be bound, consider them unequal
        return False

    # 3) Compare final argument mappings
    return norm1 == norm2
