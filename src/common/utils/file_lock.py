# 进程独占文件锁

import logging
import os
import time
from contextlib import contextmanager

from common import constants
from common import utils
from common.utils import logging_utils
from common.utils import path_utils

logger = logging.getLogger(__name__)

class FileLock:
    """
    跨平台文件独占锁

    确保同一时间只有一个进程可以访问指定文件。
    支持上下文管理器模式，自动管理锁的获取和释放。

    阻塞模式: 如果文件已被其他进程锁定，则当前进程会在{timeout}秒内等待获取文件锁或者超时。

    参数:
        file_path (str): 需要独占访问的文件路径
        blocking (bool): 是否阻塞等待锁 (默认 False)
        timeout (float): 阻塞等待锁的最大时间 (秒) (默认 30)
        lock_extension (str): 锁文件扩展名 (默认 '.lock')
    """

    # 类变量记录当前进程已加锁的文件
    _locks = {}

    def __init__(self, file_path, blocking=False, timeout=30, lock_extension='.lock'):
        logging_utils.logger_print(f"initializing file lock for path: {file_path}, blocking={blocking}, timeout={timeout}", custom_logger=logger)

        if not file_path or not isinstance(file_path, str):
            raise ValueError("file_path must be a non-empty string")
        # 获取文件全路径
        logging_utils.logger_print(f"resolving real path for: {file_path}", custom_logger=logger)
        self.file_path = path_utils.get_real_exist_file_path(file_path)
        logging_utils.logger_print(f"resolved file path: {self.file_path}", custom_logger=logger)
        if os.path.isdir(self.file_path):
            raise ValueError(f"file_path:{file_path} must be a file, not a directory")

        self.lock_path = self.file_path + lock_extension
        logging_utils.logger_print(f"lock file path: {self.lock_path}", custom_logger=logger)

        self.lock_file = None
        self.lock_acquired = False
        self.blocking = blocking
        self.timeout = timeout
        self.start_time = None
        self.atexit_registered = False

        # 检查当前进程是否已锁此文件
        if self.file_path in FileLock._locks:
            raise RuntimeError(f"file '{self.file_path}' is already locked in this process")


        # 尝试获取锁
        try:
            logging_utils.logger_print("attempting to acquire file lock", custom_logger=logger)
            self._acquire_lock()
            logging_utils.logger_print("file lock acquired successfully", custom_logger=logger)

            FileLock._locks[self.file_path] = self
            logging_utils.logger_print("file lock registered in process locks", custom_logger=logger)

            utils.shutdown_exec_funct.register(self.release)
            self.atexit_registered = True
            logging_utils.logger_print("file_lock shutdown cleanup registered", custom_logger=logger)

        except Exception:
            self._cleanup()
            logging_utils.logger_print(f"failed to acquire lock for '{self.file_path}'", custom_logger=logger, log_level=logging.ERROR)
            raise

    def _acquire_lock_unix(self):
        """Unix-like 系统文件锁实现 (Linux/macOS)"""
        import fcntl

        try:
            # 打开或创建锁文件
            self._get_lock_file(is_linux=True)

            # 非阻塞模式
            if not self.blocking:
                fcntl.flock(self.lock_file, fcntl.LOCK_EX | fcntl.LOCK_NB)
                self.lock_acquired = True
                logging_utils.logger_print(f"acquired Unix lock (non-blocking) for: {self.file_path}", custom_logger=logger, log_level=logging.DEBUG)
                return

            # 阻塞模式: 等待获取文件锁
            start_time = time.time()
            while True:
                try:
                    fcntl.flock(self.lock_file, fcntl.LOCK_EX | fcntl.LOCK_NB)
                    self.lock_acquired = True
                    logging_utils.logger_print(f"acquired Unix lock (blocking) for: {self.file_path}", custom_logger=logger, log_level=logging.DEBUG)
                    return
                except (IOError, BlockingIOError):
                    if time.time() - start_time > self.timeout:
                        raise TimeoutError(f"timeout waiting for lock on '{self.file_path}'")
                    time.sleep(0.1)
        except Exception:
            self._close_lock_file()
            logging_utils.logger_print(f"failed to acquire lock for '{self.file_path}'!", custom_logger=logger, log_level=logging.ERROR)
            raise

    def _acquire_lock_win32(self):
        """Windows 系统文件锁实现"""
        import msvcrt

        try:
            # 打开或创建锁文件 (二进制模式)
            self._get_lock_file(is_linux=False)

            # 非阻塞模式
            if not self.blocking:
                try:
                    # 锁定整个文件 (0=开始位置, 0=锁定到文件末尾)
                    self.lock_file.seek(0)
                    msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_NBLCK, 1)
                    self.lock_acquired = True
                    logging_utils.logger_print(f"acquired Windows lock (non-blocking) for: {self.file_path}", custom_logger=logger, log_level=logging.DEBUG)
                    return
                except OSError as e:
                    if e.errno in (13, 32):  # 权限错误或文件被占用
                        raise RuntimeError(f"file '{self.file_path}' is locked by another process")
                    raise

            # 阻塞模式
            start_time = time.time()
            while True:
                try:
                    self.lock_file.seek(0)
                    msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_NBLCK, 1)
                    self.lock_acquired = True
                    logging_utils.logger_print(f"acquired Windows lock (blocking) for: {self.file_path}", custom_logger=logger, log_level=logging.DEBUG)
                    return
                except OSError as e:
                    if e.errno not in (13, 32):  # 如果不是锁定错误
                        raise RuntimeError(f"unexpected error {e}")

                    if time.time() - start_time > self.timeout:
                        raise TimeoutError(f"timeout waiting for lock on '{self.file_path}'")
                    time.sleep(0.1)
        except Exception:
            self._close_lock_file()
            logging_utils.logger_print(f"failed to acquire lock for '{self.file_path}'!", custom_logger=logger, log_level=logging.ERROR)
            raise
    def _get_lock_file(self,is_linux=True):
        #不能是符号链接文件
        if os.path.lexists(self.lock_path) and os.path.islink(self.lock_path):
            raise RuntimeError(f"lock file '{self.lock_path}' is a symbolic link,cannot be locked or accessed")
        if os.path.exists(self.lock_path) and os.path.isdir(self.lock_path):
            raise RuntimeError(f"lock file path '{self.lock_path}' is a directory")
        if is_linux:
            fd = os.open(self.lock_path, os.O_CREAT | os.O_APPEND | os.O_RDWR, 0o666)
            file_mode = 'a+'
        else:
            fd = os.open(self.lock_path, os.O_CREAT | os.O_APPEND | os.O_RDWR)
            file_mode = 'a+b'
        self.lock_file = os.fdopen(fd, file_mode)
        #不能是目录
    def _acquire_lock(self):
        """跨平台文件锁获取"""
        self.start_time = time.time()

        if constants.common_constants.IS_WINDOWS:
            self._acquire_lock_win32()
        else:
            self._acquire_lock_unix()

        logging_utils.logger_print(f"lock acquired for '{self.file_path}' in {time.time() - self.start_time:.3f} seconds", custom_logger=logger)

    def _close_lock_file(self):
        """安全关闭锁文件"""
        if self.lock_file:
            try:
                self.lock_file.close()
            except Exception as e:
                logging_utils.logger_print("error closing lock file!", custom_logger=logger, use_exception=True, exception=e)
            self.lock_file = None

    def _release_lock(self):
        """释放文件锁"""
        if not self.lock_acquired or not self.lock_file:
            return
        try:
            if constants.common_constants.IS_WINDOWS:
                import msvcrt
                self.lock_file.seek(0)
                # 解锁整个文件
                msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_UNLCK, 1)
            else:
                import fcntl
                fcntl.flock(self.lock_file, fcntl.LOCK_UN)

            logging_utils.logger_print(f"released lock for '{self.file_path}'", custom_logger=logger, log_level=logging.DEBUG)
        except Exception as e:
            logging_utils.logger_print(f"failed to release lock for '{self.file_path}'!", custom_logger= logger, use_exception=True, exception=e)
        finally:
            has_lock = self._has_lock()
            self.lock_acquired = False
            self._close_lock_file()
            # 删除残留的锁文件 --- 只能是获取到文件锁的进程才能删除锁文件
            if has_lock:
                try:
                    os.remove(self.lock_path)
                except FileNotFoundError:
                    pass
                except Exception as e:
                    logging_utils.logger_print("error removing lock file!", custom_logger=logger, use_exception=True, exception=e)

    def _cleanup(self):
        """清理所有资源"""
        self._release_lock()

        # 从类变量中移除
        if self.file_path in FileLock._locks:
            del FileLock._locks[self.file_path]
        # 如果atexit已注册，现在取消注册
        if self.atexit_registered:
            try:
                utils.shutdown_exec_funct.unregister_function(self.release)
            except Exception as e:  # noqa
                logging_utils.logger_print("error unregistering atexit handler!", custom_logger=logger, use_exception=True, exception=e)
            self.atexit_registered = False

    def release(self):
        """显式释放锁"""
        logging_utils.logger_print(f"releasing lock for file: {self.file_path}", custom_logger= logger)
        if self._has_lock():
            logging_utils.logger_print(f"releasing lock for file: {self.file_path}", custom_logger= logger)
            self._cleanup()
            logging_utils.logger_print(f"lock released successfully for file: {self.file_path}", custom_logger=logger)
        else:
            logging_utils.logger_print(f"no lock to release for file: {self.file_path}", custom_logger= logger)
    def _has_lock(self):
        """检查是否已获取锁"""
        return hasattr(self, 'lock_acquired') and self.lock_acquired
    def __enter__(self):
        """支持上下文管理器 - 进入时已获取锁"""
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        """退出上下文时自动释放锁"""
        self.release()

    def __del__(self):
        """析构函数作为最后保障"""
        try:
            self.release()
        except Exception:# noqa
            pass

    def __repr__(self):
        status = "LOCKED" if self.lock_acquired else "UNLOCKED"
        return f"<FileLock for '{self.file_path}' [{status}]>"

@contextmanager
def exclusive_file_access(file_path, blocking=False, timeout=30):
    """
    上下文管理器提供文件独占访问

    使用示例:
        with exclusive_file_access("important_file.txt") as lock:
            with open(lock.file_path, 'a') as f:
                f.write("Exclusive access guaranteed!\n")
    """
    lock = FileLock(file_path, blocking=blocking, timeout=timeout)
    try:
        yield lock
    finally:
        lock.release()

def is_file_locked(file_path, lock_extension='.lock'):
    """
    检查 file_path 是否正被其他进程锁定。
    返回 True = 已锁定，False = 未锁定。
    """
    lock_path = file_path + lock_extension
    file_created = False
    fd = None
    f = None

    # 1. 统一检查锁文件是否为符号链接（不区分操作系统）
    try:
        # 使用 lexists 检查是否存在（包括符号链接）
        if os.path.lexists(lock_path):
            # 如果是符号链接，直接返回已锁定
            if os.path.islink(lock_path):
                return True
            # 如果是目录，也视为已锁定
            if os.path.isdir(lock_path):
                return True
    except OSError:
        # 路径无效或权限问题视为已锁定
        return True
    try:
        # 2) 原子打开（并创建），Unix 上加 O_NOFOLLOW
        flags = os.O_RDWR | os.O_CREAT | os.O_EXCL  # 使用 O_EXCL 确保原子创建
        if hasattr(os, 'O_NOFOLLOW'):
            flags |= os.O_NOFOLLOW

        try:
            fd = os.open(lock_path, flags, 0o666)
            file_created = True  # 标记为当前进程创建
        except FileExistsError:
            # 文件已存在，使用普通打开方式
            flags = os.O_RDWR
            if hasattr(os, 'O_NOFOLLOW'):
                flags |= os.O_NOFOLLOW
            try:
                fd = os.open(lock_path, flags, 0o666)
            except OSError:
                return True
        except OSError:
            # 任何打开失败都视作"已锁定"
            return True

        # 3) 用文件对象做锁/解锁
        f = os.fdopen(fd, 'a+b')
        fd = None  # 文件对象已接管，不再单独 close(fd)
        if constants.common_constants.IS_WINDOWS:
            import msvcrt
            try:
                f.seek(0)
                # 尝试抢锁第 1 字节（非阻塞）
                msvcrt.locking(f.fileno(), msvcrt.LK_NBLCK, 1)

                # 一旦抢到，马上解锁
                f.seek(0)
                msvcrt.locking(f.fileno(), msvcrt.LK_UNLCK, 1)
                return False
            except OSError:
                return True

        else:
            import fcntl
            try:
                # 尝试非阻塞独占锁
                fcntl.flock(f.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)

                # 一旦抢到，马上解锁
                fcntl.flock(f.fileno(), fcntl.LOCK_UN)
                return False
            except (OSError, BlockingIOError):
                return True
    finally:
        # 4) 清理资源 - 添加异常保护
        try:
            if f is not None:
                f.close()
            elif fd is not None:
                os.close(fd)
        except OSError:
            # 忽略关闭时的任何错误
            pass
            # 5. 如果创建了临时锁文件，现在删除它
        if file_created:
            try:
                os.remove(lock_path)
            except OSError:
                # 忽略删除错误，可能已被其他进程删除
                pass
