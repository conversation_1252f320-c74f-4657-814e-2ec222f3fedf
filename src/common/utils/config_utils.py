"""配置工具模块。

此模块提供了配置文件处理相关的工具函数，包括：
- 配置文件解析和验证
- 配置节转换和更新
- 时区和端口配置检查
- 配置文件同步操作
"""

import configparser
import os
from typing import Any, Dict
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError

from common.constants import common_constants
from common.utils.common_utils import trim
from common.utils.path_utils import get_real_path_create_dir


def tran_int(key: str, value: str) -> int:
    """配置项key对应的值必须可以转换成整数
    
    Args:
        key: 配置项键名
        value: 配置项值
        
    Returns:
        int: 转换后的整数值
        
    Raises:
        ValueError: 值无法转换为整数
    """
    try:
        return int(value)
    except ValueError as e:
        raise ValueError(f"config key:{key} - value:{value} must be a valid integer!") from e


def section_to_dict(config: configparser.ConfigParser, section_name: str, allow_value_none: bool = False, allow_empty_section: bool = False) -> Dict[str, Any]:
    """该函数用于将配置文件中对应section节点内容并转换为字典
    
    Args:
        config: 配置解析器对象
        section_name: 节点名称
        allow_value_none: 是否允许值为None
        allow_empty_section: 是否允许空节点
        
    Returns:
        Dict[str, Any]: 配置项字典
        
    Raises:
        ValueError: 节点不存在、键为空、值为空等错误
    """
    if section_name is None or not config.has_section(section_name):
        raise ValueError(f"current section :{section_name} not exist in config file!")
    config_file_section = config[section_name]
    res = {}
    for raw_key, raw_value in config_file_section.items():
        key = trim(raw_key)
        if key is None:
            raise ValueError(f"{section_name} section key can not be empty,current key is {raw_key}")
        if key in res:
            raise ValueError(f"{section_name} section key can not be duplicated,current key is {raw_key}")
        value = trim(raw_value)
        if value is None and not allow_value_none:
            raise ValueError(
                f"{section_name} section value can not be empty,current key is {raw_key},current value is {raw_value}")
        res[key] = value
    if not res and not allow_empty_section:
        raise ValueError(f"{section_name} section can not be empty!")
    return res


def check_time_zone_str(time_zone_str: str):
    """检查时区字符串是否有效
    
    Args:
        time_zone_str: 时区字符串
        
    Raises:
        ValueError: 时区字符串无效
    """
    try:
        ZoneInfo(time_zone_str)
    except ZoneInfoNotFoundError:
        raise ValueError(
            f"server properties [server]-[time_zone] value must be a valid timezone,current value is {time_zone_str}")


def get_server_config_port(port_str: str) -> int:
    """该函数用于获取服务端配置的端口号
    
    Args:
        port_str: 端口字符串
        
    Returns:
        int: 端口号
        
    Raises:
        ValueError: 端口号无效或超出范围
    """
    port_int = tran_int("port", port_str)
    if not (common_constants.MIN_PORT <= port_int <= common_constants.MAX_PORT):
        raise ValueError(
            f"server properties [server]-[port] value must be between {common_constants.MIN_PORT} and {common_constants.MAX_PORT},current value is {port_str}")
    return port_int


def update_config(file_path, section, config_dict):
    """更新配置文件中的指定节点内容

    Args:
        file_path: 配置文件路径
        section: 节点名称
        config_dict: 要更新/添加的配置项字典 {配置项: 值}
    """
    config = configparser.ConfigParser(interpolation=None)
    # 保留原文件大小写（默认转换为小写）
    config.optionxform = lambda option: option
    file_path = get_real_path_create_dir(file_path, path_is_dir=False)
    # 如果文件存在则读取
    if os.path.exists(file_path):
        config.read(file_path, encoding='utf-8')

    # 如果节点不存在则创建
    if not config.has_section(section):
        config.add_section(section)

    # 更新/添加所有配置项
    for key, value in config_dict.items():
        config.set(section, key, str(value))

    # 写入文件（自动创建新文件）
    with open(file_path, 'w', encoding='utf-8') as configfile:
        config.write(configfile)  # type: ignore


def synch_section(file_path, section, config_dict):
    """同步配置文件中的指定节点内容，该配置文件中的其他节点的内容不变

    Args:
        file_path: 配置文件路径
        section: 节点名称
        config_dict: 需要完全同步的配置项字典 {配置项: 值}
    """
    config = configparser.ConfigParser(interpolation=None)
    # 保留原文件大小写（默认转换为小写）
    config.optionxform = lambda option: option
    file_path = get_real_path_create_dir(file_path, path_is_dir=False)

    # 如果文件存在则读取
    if os.path.exists(file_path):
        config.read(file_path, encoding='utf-8')

    # 确保 section 存在
    if not config.has_section(section):
        config.add_section(section)

    # 清空原 section 的所有项
    for key in list(config[section].keys()):
        config.remove_option(section, key)

    # 添加新的配置项
    for key, value in config_dict.items():
        config.set(section, key, str(value))

    # 写入文件（保留其他 section）
    with open(file_path, 'w', encoding='utf-8') as f:
        config.write(f)  # type: ignore
