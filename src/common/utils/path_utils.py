"""文件路径工具模块。

此模块提供了文件和路径处理相关的工具函数，包括：
- 路径解析和验证
- 文件哈希计算
- 文件编码检测
- 目录创建和管理
"""

import hashlib
import logging
import os
import tempfile
from typing import Literal

import chardet

logger = logging.getLogger(__name__)


def get_real_path(path: str) -> str:
    """获取指定文件路径的真实全路径
    
    Args:
        path: 文件路径
        
    Returns:
        str: 真实全路径
        
    Raises:
        ValueError: 路径为空或无效
    """
    if not path or not isinstance(path, str):
        raise ValueError(f"path must be a non-empty string,current value is {path}")
    return os.path.realpath(os.path.expanduser(path))


def get_real_exist_file_path(path: str) -> str:
    """获取本地文件路径实际对应的全路径:如果文件不存在则抛出异常
    
    Args:
        path: 文件路径
        
    Returns:
        str: 存在文件的真实全路径
        
    Raises:
        ValueError: 文件不存在或路径无效
    """
    try:
        real_path = get_real_path(path)
        if not os.path.isfile(real_path):
            raise ValueError(f"file not exist: {path}")
        return real_path
    except OSError as e:
        raise ValueError(f"invalid file path: {path}") from e


def get_real_path_create_dir(path: str, path_is_dir: bool = False) -> str:
    """获取对应路径的真实路径,并创建目录:[如果path是目录,则创建目录 --- path_is_dir=True]
    
    Args:
        path: 文件或目录路径
        path_is_dir: 是否为目录路径
        
    Returns:
        str: 真实路径
    """
    from common.utils.logging_utils import logger_print
    
    try:
        real_path = get_real_path(path)
        # make sure the directory exists
        dir_path = real_path if path_is_dir else os.path.dirname(real_path)
        os.makedirs(dir_path, exist_ok=True)
        return real_path
    except Exception as e:
        # 在PyInstaller环境下，如果路径创建失败，尝试使用临时目录
        logger_print(f"failed to create directory for path {path}, using temp directory: {e}", custom_logger=logger, log_level=logging.WARNING)

        if path_is_dir:
            # 如果是目录，在临时目录下创建
            temp_dir = tempfile.mkdtemp(prefix="webhook_server_")
            return temp_dir
        else:
            # 如果是文件，在临时目录下创建文件路径
            temp_dir = tempfile.mkdtemp(prefix="webhook_server_")
            filename = os.path.basename(path)
            return os.path.join(temp_dir, filename)


def is_utf8_encoding_file(file_path, chunk_size=4096):
    """判断指定文件路径对应文件是否是UTF-8编码的文件
    
    前提:
    1. 文件必须存在
    2. 文件必须可读
    
    Args:
        file_path: 文件路径
        chunk_size: 读取块大小
        
    Returns:
        bool: 是否为UTF-8编码文件
    """
    from common.utils.logging_utils import logger_print
    
    file_path = os.path.realpath(os.path.expanduser(file_path))
    if not os.path.isfile(file_path) or not os.access(file_path, os.R_OK):
        return False
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(chunk_size)
            if not chunk:
                # 空文件被视为合法的UTF-8编码文件
                return True
            file_info = chardet.detect(chunk)
            encoding = file_info.get('encoding', '').lower()
            confidence = file_info.get('confidence', 0.0)
            logger_print(f"文件{file_path}的编码为{encoding},置信度为{confidence}", custom_logger=logger, log_level=logging.DEBUG)
            return encoding in ['utf-8', 'utf-8-sig', 'ascii'] and confidence > 0.9
    except UnicodeDecodeError:
        return False
    except Exception:  # noqa
        logger_print("检查编码时出错!", custom_logger=logger, use_exception=True)
        return False


def file_hash(file_path: str,
        algorithm: Literal[
            "md5", "sha1", "sha224", "sha256", "sha384", "sha512",
            "sha3_224", "sha3_256", "sha3_384", "sha3_512",
            "blake2b", "blake2s"
        ] = "sha256",
        chunk_size: int = 65536
) -> str:
    """计算文件的哈希值。

    Args:
        file_path: 要读取并计算哈希值的文件路径。
        algorithm: 哈希算法名称，默认 'sha256'。可选 'md5', 'sha1',
                   'sha224', 'sha384', 'sha512', 'sha3_224', 'sha3_256',
                   'sha3_384', 'sha3_512', 'blake2b', 'blake2s' 等。
        chunk_size: 每次读取的字节数，默认 65536。

    Returns:
        文件的十六进制哈希字符串。

    Raises:
        ValueError: 文件不存在或无法读取，或不支持的哈希算法
    """
    if chunk_size <= 0:
        raise ValueError("chunk_size must be greater than 0!")
    if not os.path.isfile(file_path):
        raise ValueError(f"文件{file_path}实际不存在,无法计算哈希值!")
    # 创建对应算法的哈希对象
    try:
        hasher = hashlib.new(algorithm)
    except ValueError:
        raise ValueError(f"不支持的哈希算法: {algorithm}")

    # 分块读取文件并更新哈希
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                hasher.update(chunk)
            return hasher.hexdigest()
    except OSError:
        raise ValueError(f"文件{file_path}无法读取,无法计算哈希值!")
