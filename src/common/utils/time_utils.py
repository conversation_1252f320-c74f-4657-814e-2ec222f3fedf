"""时间工具模块。

此模块提供了时间处理相关的工具函数，包括：
- 日期时间计算和格式化
- 时间间隔处理
- 运行时间检查
- 显示时间格式化
"""

import time
from datetime import datetime, timedelta
from typing import Tuple
from zoneinfo import ZoneInfo

from common.constants import common_constants


def get_next_datetime(start_time: datetime, target_time: datetime.time) -> datetime:
    """获取大于start_time时间的符合target_time的 datetime 对象（包含年月日时分秒）;过期的不要
    
    Args:
        start_time: 起始时间
        target_time: 目标时间
        
    Returns:
        datetime: 下一个符合条件的日期时间
    """
    # 获取和启动时间相同的date的 target_time 时间
    date_start_time = start_time.date()
    date_target = datetime.combine(date_start_time, target_time, tzinfo=start_time.tzinfo)
    if start_time < date_target:
        return date_target

    # 构造明天的 target_time 时间
    tomorrow_date = date_start_time + timedelta(days=1)
    return datetime.combine(tomorrow_date, target_time, tzinfo=start_time.tzinfo)


def format_time_monotonic_interval(start_timestamp):
    """计算两个monotonic时间戳间隔，并格式化为 "xxx天xx:xx:xx"

    Args:
        start_timestamp (float): 输入时间戳（time.monotonic()格式） 起始时间

    Returns:
        tuple: (时间差秒数, 格式化的时间间隔字符串)
    """
    # 1. 获取当前时间戳
    current_timestamp = time.monotonic()

    # 2. 计算时间差（秒）
    time_diff = abs(current_timestamp - start_timestamp)

    # 3. 分解时间差为天、小时、分钟、秒
    days = int(time_diff // (24 * 3600))
    remaining_seconds = time_diff % (24 * 3600)

    hours = int(remaining_seconds // 3600)
    remaining_seconds %= 3600

    minutes = int(remaining_seconds // 60)
    seconds = int(remaining_seconds % 60)

    # 4. 格式化为字符串 (xx:xx:xx 固定2位)
    return time_diff, f"{days}天 {hours:02d}:{minutes:02d}:{seconds:02d}"


def format_time_for_display(time_str: str) -> str:
    """将字符串时间转换成前端gui显示格式,如:1小时前,5分钟前;
    
    Args:
        time_str: 字符串时间,格式为'%Y-%m-%d %H:%M:%S'
                 由于`time_str`是从数据库中获取到的,其值已经经过时区转换,所以这里不需要再转换
                 
    Returns:
        str: 格式化的显示时间
    """
    if not time_str:
        return "未知"
    past = datetime.strptime(time_str, common_constants.DATETIME_FORMAT)
    now = datetime.now()
    delta = now - past

    # 1 分钟内：秒
    seconds = int(delta.total_seconds())
    if seconds < 60:
        return f"{seconds}秒前"

    # 1 小时内：分钟
    minutes = seconds // 60
    if minutes < 60:
        return f"{minutes}分钟前"

    # 1 天内：小时
    hours = minutes // 60
    if hours < 24:
        return f"{hours}小时前"

    # 1 个月内：天（这里按30天计算）
    days = hours // 24
    if days < 30:
        return f"{days}天前"

    # 1 年内：月（按年月差计算，更准确）
    # 先计算年月总差值
    year_diff = now.year - past.year
    month_diff = now.month - past.month
    total_months = year_diff * 12 + month_diff
    if total_months < 12:
        return f"{total_months}月前"

    # 超过 1 年：年
    # 如果当前月-日还没到过 past 的月-日，则实足年数要减 1
    years = year_diff
    if (now.month, now.day) < (past.month, past.day):
        years -= 1
    return f"{years}年前"


def check_runtime_allowed(run_time_str: str, current_time_zone: ZoneInfo) -> Tuple[bool, bool, datetime.time, datetime.time, datetime.time]:
    """检查当前时间是否在允许的运行时间段内
    
    Args:
        run_time_str: 运行时间段字符串，格式为 "HH:MM-HH:MM"，如 "07:00-19:00"
        current_time_zone: 当前时区
        
    Returns:
        Tuple[bool, bool, datetime.time, datetime.time, datetime.time]: 
        (can_run, always_run, start_time, end_time, now)
        - can_run: 是否可以运行
        - always_run: 是否一直运行（开始时间和结束时间相同）
        - start_time: 开始时间
        - end_time: 结束时间  
        - now: 当前时间
    """
    start_time, end_time = run_time_str.split('-')
    start_time = datetime.strptime(start_time.strip(), "%H:%M").time()
    end_time = datetime.strptime(end_time.strip(), "%H:%M").time()
    now = datetime.now(current_time_zone).time()
    
    # 开始时间和结束时间一致,则一直运行
    always_run = (start_time == end_time)
    
    # 支持跨天运行
    can_run = (always_run or (start_time <= now <= end_time)
               or (end_time < start_time and (now <= end_time or now >= start_time)))
    
    return can_run, always_run, start_time, end_time, now
