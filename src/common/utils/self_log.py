"""日志系统配置模块。

此模块提供了项目全局统一的日志配置功能，包括：
- 彩色控制台输出
- 文件日志轮转
- 时区支持
- 日志清理
- uvicorn日志配置
"""

import configparser
import logging
import os
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
from typing import Any, Dict
from zoneinfo import ZoneInfo

from colorama import Fore, Style, init as colorama_init

from common.constants import common_constants
from common.utils.common_utils import run_once
from common.utils.config_utils import check_time_zone_str

# 提供控制台颜色输出支持
colorama_init()

def configure_uvicorn() -> None:
    """配置uvicorn日志，使其使用项目的日志配置。"""
    for name in ["uvicorn", "uvicorn.error", "uvicorn.access"]:
        logger = logging.getLogger(name)
        logger.handlers = []
        logger.propagate = True

class ColorFormatter(logging.Formatter):
    """支持彩色输出的日志格式化器。

    Attributes:
        colors: 日志级别对应的颜色映射
    """

    def __init__(self, fmt=None, date_fmt=None, colors=None, tz=None):
        """初始化彩色格式化器。

        Args:
            fmt: 日志格式字符串
            date_fmt: 日期格式字符串
            colors: 颜色映射字典
            tz: 时区信息
        """
        super().__init__(fmt=fmt, datefmt=date_fmt)
        self.colors = colors or {}
        if tz:
            self.converter = lambda *args: datetime.now(tz).timetuple()

    def format(self, record) -> str:
        """格式化日志记录，添加颜色。

        Args:
            record: 日志记录对象

        Returns:
            str: 格式化后的彩色日志字符串
        """
        color = self.colors.get(record.levelname, "")
        message = super().format(record)
        return f"{color}{message}{Style.RESET_ALL}"

class LogConfig:
    """日志配置单例类。

    管理全局日志配置，包括文件日志、控制台日志、日志清理等功能。

    Attributes:
        config: 配置解析器对象
        base_dir: 日志基础目录
        zone: 时区信息
        filename_prefix: 日志文件名前缀
        expire_logs_days: 日志过期天数
    """
    _instance = None

    def __init__(self):
        """初始化日志配置。"""
        self.config = None
        self.base_dir = None
        self.zone = None
        self.filename_prefix = "app"
        self.expire_logs_days = 0
        self._initialized = False

    def __new__(cls):
        """单例模式实现。"""
        if not cls._instance:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    def had_init(self)->bool:
        return self._initialized
    # 将日志配置文件中配置项中空值设置默认值并转换成指定类型
    @staticmethod
    def get_log_config(config: configparser.ConfigParser) -> Dict[str, Any]:
        from common.utils.config_utils import section_to_dict
        new_log_config = section_to_dict(config, "log", True)
        LogConfig.check_reset_log_config(new_log_config)
        return new_log_config

    @staticmethod
    def check_reset_log_config(new_log_config: dict):
        level_value = new_log_config.get("level")
        if level_value is None:
            new_log_config["level"] = common_constants.LOG_LEVEL
        elif level_value.upper() not in common_constants.LOG_LEVELS:
            raise ValueError(f"invalid log level: {new_log_config['level']}")
        else:
            new_log_config["level"] = level_value.upper()
        from common.utils.common_utils import str_to_bool
        console_value = str_to_bool(new_log_config.get("console"))
        new_log_config["console"] = console_value if console_value is not None else common_constants.LOG_CONSOLE
        file_value = str_to_bool(new_log_config.get("file"))
        new_log_config["file"] = file_value if file_value is not None else common_constants.LOG_FILE
        log_dir_value = new_log_config.get("dir")
        if log_dir_value is None:
            log_dir_value = common_constants.LOG_DIR
        from common.utils.path_utils import get_real_path_create_dir
        new_log_config["dir"] = get_real_path_create_dir(log_dir_value, True)
        filename_prefix_value = new_log_config.get("filename_prefix")
        if filename_prefix_value is None:
            new_log_config["filename_prefix"] = common_constants.LOG_FILE_NAME_PREFIX
        filename_date_fmt_value = new_log_config.get("filename_date_fmt")
        if filename_date_fmt_value is None:
            new_log_config["filename_date_fmt"] = common_constants.LOG_FILE_DATE_FMT
        filename_value = new_log_config.get("filename")
        if filename_value is None:
            new_log_config["filename"] = common_constants.LOG_FILENAME
        max_size_value = new_log_config.get("max_size")
        if max_size_value is None:
            max_size_value = common_constants.LOG_FILE_MAX_SIZE
        from common.utils.common_utils import convert_storage_str_to_bytes
        new_log_config["max_size"] = convert_storage_str_to_bytes(max_size_value)
        backup_count_value = new_log_config.get("backup_count")
        if backup_count_value is None:
            backup_count_value = common_constants.LOG_BACKUP_COUNT
        new_log_config["backup_count"] = int(backup_count_value)
        format_value = new_log_config.get("format")
        if format_value is None:
            new_log_config["format"] = common_constants.LOG_FORMAT
        date_fmt_value = new_log_config.get("date_fmt")
        if date_fmt_value is None:
            new_log_config["date_fmt"] = common_constants.LOG_DATE_FMT
        expire_logs_days_value = new_log_config.get("expire_logs_days")
        if expire_logs_days_value is None:
            expire_logs_days_value = common_constants.LOG_EXPIRE_LOGS_DAYS
        new_log_config["expire_logs_days"] = int(expire_logs_days_value)
        zone_value = new_log_config.get("zone")
        if zone_value is None:
            new_log_config["zone"] = common_constants.TIME_ZONE
        else:

            check_time_zone_str(zone_value) # 存在时校验其格式有效性

    @staticmethod
    def _set_config_value_from_param(had_log_config:dict[str, Any],key:str,value:Any=None):
        # 将从configure函数入参传入的配置项值设置到基础log配置[basic_log_config]中
        if value is not None:
            had_log_config[key] = value
    def configure(self, config_path: str, time_zone: ZoneInfo,filename_prefix:str=None):
        if self._initialized:
            return
        self.config = configparser.ConfigParser(interpolation=None)
        self.config.read(config_path, encoding="utf-8")

        # 基础配置
        basic_log_config = LogConfig.get_log_config(self.config)
        LogConfig._set_config_value_from_param(basic_log_config,"filename_prefix",filename_prefix)
        self.base_dir = basic_log_config["dir"]
        self.expire_logs_days = basic_log_config["expire_logs_days"]
        if time_zone:
            self.zone = time_zone
        else:
            self.zone = ZoneInfo(basic_log_config["zone"])
        # 设置logging时区
        logging.Formatter.converter = lambda *args: datetime.now(self.zone).timetuple()

        # 配置根记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(basic_log_config["level"])

        # 清理现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 控制台输出
        if basic_log_config["console"]:
            self._setup_console_handler(basic_log_config)

        # 文件输出
        if basic_log_config["file"]:
            self._setup_file_handlers(basic_log_config)

        # 配置uvicorn日志
        configure_uvicorn()

        # 清理过期日志 [初始化时执行一次]
        self._clean_expired_logs()
        self._initialized = True

    def _setup_console_handler(self, config: dict):
        console_fmt = config["format"]
        console_date_fmt = config["date_fmt"]
        colors = self._parse_colors()

        console_handler = logging.StreamHandler()
        console_handler.setFormatter(
            ColorFormatter(console_fmt, console_date_fmt, colors, tz=self.zone)
        )
        logging.getLogger().addHandler(console_handler)

    def _setup_file_handlers(self, config: dict):
        file_fmt = config["format"]
        date_fmt = config["date_fmt"]
        filename_prefix = config["filename_prefix"]
        filename_date_fmt = config["filename_date_fmt"]
        filename_template = config["filename"]
        max_size = config["max_size"]
        backup_count = config["backup_count"]

        formatter = logging.Formatter(file_fmt, date_fmt)
        formatter.converter = lambda *args: datetime.now(self.zone).timetuple()
        self.filename_prefix = filename_prefix
        if "%(levelname)s" in filename_template:
            for level in common_constants.LOG_LEVELS:
                self._create_file_handler(
                    level, filename_template, filename_prefix, filename_date_fmt,
                    formatter, max_size, backup_count
                )
        else:
            self._create_file_handler(
                "", filename_template, filename_prefix, filename_date_fmt,
                formatter, max_size, backup_count
            )

    def _create_file_handler(self, level, template, filename_prefix, filename_date_fmt, formatter, max_size, backups):
        filename = template.replace("%(filename_prefix)s", filename_prefix)
        filename = filename.replace("%(levelname)s", level.lower() if level else "")
        if "%(filename_date_fmt)s" in filename:
            current_time = datetime.now(self.zone)
            filename = filename.replace(
                "%(filename_date_fmt)s",
                current_time.strftime(filename_date_fmt)
            )
        filename = str(os.path.join(self.base_dir, filename))
        handler = RotatingFileHandler(
            filename=filename,
            maxBytes=max_size,
            backupCount=backups,
            encoding="utf-8"
        )
        handler.setFormatter(formatter)
        if level:
            handler.setLevel(level)
            handler.addFilter(lambda r: r.levelname == level)
        logging.getLogger().addHandler(handler)

    def _clean_expired_logs(self):
        # 日志过期时间为0则不清理:表示永不过期
        if self.expire_logs_days <= 0:
            return

        logging.info(f"cleaning expired logs older than {self.expire_logs_days} days")
        cutoff = datetime.now(self.zone) - timedelta(days=self.expire_logs_days)
        cutoff_timestamp = cutoff.timestamp()

        for filename in os.listdir(self.base_dir):
            filepath = os.path.join(self.base_dir, filename)

            if os.path.isfile(filepath) and filename.endswith(".log") and filename.startswith(
                    self.filename_prefix + "-"):
                file_mtime = os.path.getmtime(filepath)
                if file_mtime < cutoff_timestamp:
                    try:
                        logging.warning(f"deleting expired log file {filepath}")
                        os.remove(filepath)
                    except Exception: # noqa
                        logging.exception(f"failed to delete {filepath}!")

    def _parse_colors(self):
        colors = {}
        if self.config.has_section("colors"):
            for level, value in self.config.items("colors"):
                colors[level.upper()] = self._parse_color(value)
        return colors

    @staticmethod
    def _parse_color(color_str):
        color_code = ""
        for part in color_str.strip().split("+"):
            attr = getattr(Fore, part.strip(), None) or getattr(Style, part.strip(), None)
            if attr:
                color_code += attr
        return color_code


# 全局配置入口:全局唯一[单例模式] --- 一个进程只需要初始化执行一次即可
log_config = LogConfig()

def setup_logging(config_path: str, time_zone: ZoneInfo = None,filename_prefix:str=None):
    run_once(log_config.configure, config_path=config_path, time_zone=time_zone, filename_prefix=filename_prefix)
