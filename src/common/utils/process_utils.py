"""进程工具模块。

此模块提供了进程管理相关的工具函数，包括：
- 独立进程启动监控
- 进程输出处理
- 进程状态检查
"""

import logging
import subprocess
import time

from common.utils.logging_utils import logger_print

logger = logging.getLogger(__name__)


def monitor_independent_process_startup(independent_process: subprocess.Popen) -> int | None:
    """等待确认指定进程是否启动成功
    
    Args:
        independent_process: 独立进程对象
        
    Returns:
        int | None: None 表示启动成功，其他值表示该进程退出码
    """
    logger_print(msg="waiting for independent_process startup confirmation", custom_logger=logger)
    max_wait_time = 4  # 最大等待时间（秒）
    check_interval = 0.2  # 检查间隔（秒）
    elapsed_time = 0

    while elapsed_time < max_wait_time:
        # 检查进程是否还在运行
        poll_result = independent_process.poll()
        # 0表示正常退出，非0表示异常退出
        if poll_result is not None and poll_result != 0:
            # 进程已结束，启动失败
            logger_print(msg=f"independent_process process exited with code: {poll_result}", custom_logger=logger)

            # 尝试读取进程输出以获取更多错误信息
            process_end_output(independent_process)

            return poll_result

        time.sleep(check_interval)
        elapsed_time += check_interval

    return None


def process_end_output(independent_process: subprocess.Popen):
    """对应进程在结束之后打印其输出
    
    Args:
        independent_process: 独立进程对象
    """
    if independent_process is None or independent_process.poll() is None:
        return
    try:
        if hasattr(independent_process, 'stdout') and independent_process.stdout:
            stdout_output = independent_process.stdout.read()
            if stdout_output:
                logger_print(msg=f"process stdout: {stdout_output}", custom_logger=logger)
        if hasattr(independent_process, 'stderr') and independent_process.stderr:
            stderr_output = independent_process.stderr.read()
            if stderr_output:
                logger_print(msg=f"process stderr: {stderr_output}", custom_logger=logger)
    except Exception:  # noqa
        pass
