"""日志工具模块。

此模块提供了日志记录相关的工具函数，包括：
- 兼容性日志打印
- 临时文件日志记录
- 异常处理和输出
"""

import logging
import sys
import traceback
from datetime import datetime
from typing import Optional

from common import constants
from common import utils


def logger_print(msg: str, custom_logger: Optional[logging.Logger], log_level: int = logging.INFO, use_exception: bool = False, exception: Optional[BaseException] = None):
    """打印日志信息 --- 在对于logger可能为空的情况下,兼容print打印日志信息
    
    Args:
        msg: 日志消息
        custom_logger: 自定义logger，可以为None
        log_level: 日志级别，默认INFO
        use_exception: 是否使用异常模式
        exception: 异常对象，可选
    """
    show_msg = msg if exception is None else f"{msg} \n {exception}"
    if custom_logger is None or not utils.self_log.log_config.had_init():
        print_tmp_file(msg, print_error=use_exception or exception or log_level >= logging.WARNING, exception=exception)
        if use_exception or exception:
            traceback.print_exc(file=sys.stderr)
        return
    
    if use_exception:
        custom_logger.exception(show_msg)
    else:
        custom_logger.log(log_level, show_msg)


def print_tmp_file(msg: str, print_error: bool = False, exception: Optional[BaseException] = None):
    """在没有logger的情况下,将日志信息打印到临时文件中
    
    Args:
        msg: 日志消息
        print_error: 是否为错误信息
        exception: 异常对象，可选
    """
from common.utils import path_utils
    
    show_msg = f"{msg}" if exception is None else f"{msg} \n {exception}"
    now = datetime.now(constants.common_constants.DEFAULT_TIMEZONE)
    now_str = now.strftime(constants.common_constants.DATETIME_FORMAT)
    show_msg = f"{now_str} {show_msg}"

    try:
        tmp_file_path = constants.common_constants.TEMP_LOG_FILE_PATH + now.strftime("%Y-%m-%d") + ".log"
        tmp_log_file = path_utils.get_real_path_create_dir(tmp_file_path, path_is_dir=False)
        with open(tmp_log_file, "a", encoding="utf-8") as f:
            f.write(show_msg)
            f.write("\n")
    except Exception as file_error:
        # 如果文件写入失败，尝试安全地输出到stderr
        try:
            print(f"Failed to write to temp log file: {file_error}", file=sys.stderr)
        except (OSError, ValueError):
            # 如果stderr也不可用，则忽略
            pass

    # 安全地输出到标准流
    try:
        if exception or print_error:
            print(show_msg, file=sys.stderr)
        else:
            print(show_msg)
    except (OSError, ValueError):
        # 如果标准流不可用（如在exe环境下被重定向），则忽略
        # 这种情况下，至少日志文件中会有记录
        pass
