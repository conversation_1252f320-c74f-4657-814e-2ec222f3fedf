"""使用ttkbootstrap库实现GUI界面的工具类模块。

此模块提供了基于ttkbootstrap的GUI工具函数，包括：
- 字体管理和设置
- 消息对话框封装
- GUI组件样式配置
- 界面布局工具
"""

import functools
import logging
import tkinter
from tkinter import font
from typing import Callable, Optional, Union

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import MessageDialog

from common import constants
from common import models
from common import utils
from common.utils import logging_utils

logger = logging.getLogger(__name__)
first_available_font_family: Optional[str] = None


def set_available_unified_font_families(main_win: ttkb.Window) -> None:
    """给当前GUI主界面设置统一的可用的字体。

    Args:
        main_win: ttkbootstrap主窗口对象
    font name统一，每一个控件可以单独修改字体，但是整体上是使用同一种字体
    注意事项：必须在其他控件之前设置字体列表，否则已有的控件不会生效


    :param main_win: ttkbootstrap.Window 当前gui主界面
    """
    global first_available_font_family
    if not main_win:
        logging_utils.logger_print(msg="set available font families failed,main_win is None", custom_logger=logger, log_level=logging.ERROR)
        raise ValueError("set available font families failed,main_win is None")

    def get_first_available_font_family():
        """加载第一个有效的字体"""
        all_font_families=set(font.families())
        for family in constants.gui_const.FONT_FAMILIES:
            if family in all_font_families:
                return family
        return font.nametofont("TkDefaultFont").actual()["family"]
    if first_available_font_family is None:
         first_available_font_family=get_first_available_font_family()
    # font.Font的形式对有的控件不生效
    cur_font=(first_available_font_family,11)
    main_win.option_add('*Font',cur_font)
    ttkb.Style().configure(".", font=cur_font)

def dialog_center_in_parent(parent:Union[tkinter.Tk,tkinter.Toplevel], dialog:MessageDialog):
    """对话框在父窗口居中显示"""
    parent.update_idletasks()

    pw = parent.winfo_width()
    ph = parent.winfo_height()
    px = parent.winfo_rootx()
    py = parent.winfo_rooty()

    dw = constants.gui_const.MESSAGE_DIALOG_WIDTH
    dh = constants.gui_const.MESSAGE_DIALOG_HEIGHT

    x = px + (pw - dw) // 2
    y = py + (ph - dh) // 2
    dialog.show(position=(x, y))

def comm_child_win_do(child_win:tkinter.Toplevel,parent_win:Union[tkinter.Tk, tkinter.Toplevel]):
    """设置子窗口的通用操作"""
    child_win.withdraw()  # 先隐藏窗口
    # child_win.attributes("-toolwindow", True)  # 避免出现闪烁
    utils.gui_utils.center_dialog_on_parent(child_win, parent_win)
    child_win.grab_set()

    child_win.resizable(False, False)
    child_win.focus_force()
    child_win.deiconify()


def create_cpu_meter(parent,metersize,meterthickness):
    meter = models.gui_widgets.PrecisionMeter(
        master=parent,
        metersize=metersize, # 仪表盘直径
        meterthickness=meterthickness, # 仪表盘圆弧的粗细程度
        stripethickness=0,
        wedgesize=0,
        amountused=0.0,  # 使用浮点数
        amounttotal=100.0,  # 使用浮点数
        amountformat="{:.2f}",  # 浮点数格式
        metertype="full",
        subtext="CPU 使用率",
        interactive=False,
        textleft="",
        textright="%",
        textfont=("Helvetica", 18, "bold"),
        subtextfont=("Helvetica", 10),
        bootstyle="info"
    )

    return meter

# 圆形仪表盘在不同值时的默认显示样式
default_segment_style={50:"success",80:"warning",100:"danger"}
def update_cpu_meter(meter, value,segment_style:dict[int,str]=None):
    """
    更新仪表盘显示
    根据仪表盘区域的阈值和样式，自动调整样式
    取值范围:(0,100) 不包含边界值
    :param meter: PrecisionMeter 仪表盘控件
    :param value: float 仪表盘显示的值
    :param segment_style: dict[int,str] 阈值和样式的映射关系 其key必须按照从小到大排列
    """
    if segment_style is None:
        segment_style=default_segment_style
    if value < 0:
        logging_utils.logger_print(msg=f"update cpu meter failed,value is less than 0,current value is {value}", custom_logger=logger, log_level=logging.ERROR)
        value = 0.01
    elif value >= 100:
        logging_utils.logger_print(msg=f"update cpu meter failed,value is greater than 100,current value is {value}", custom_logger=logger, log_level=logging.ERROR)
        value = 99.99
    cur_style="success"
    for segment_threshold in segment_style.keys():
        if value < segment_threshold:
            cur_style = segment_style[segment_threshold]
            break

    # 应用样式
    meter.configure(bootstyle=cur_style,amountused=value)

def show_notification(win:ttkb.Window, text:str, font_family_size:tuple[str, int]):
    """设置在主界面上显示通知栏"""
    notification_container = ttkb.Frame(win)
    notification_container.pack(side=TOP, fill=X)
    # 禁止自动缩放，这样即便子控件被隐藏，容器高度也不会改变
    notification_container.pack_propagate(False)
    # 固定容器高度，与 Marquee 一致
    notification_container.configure(height=font_family_size[1] + 8 + 8)  # font size(10) + padding(8)
    # 在容器中放入通知栏
    notif = models.gui_widgets.NotificationBar(notification_container, text=text, font_family_size=font_family_size)
    notif.pack(fill=BOTH, expand=True)

def entry_event_validate(event, validate_func: Callable[[str], bool], original_style: str=None):
    """
    event控件的事件触发时验证 entry 内容是否合法：
    - 若合法，恢复原始样式
    - 若不合法，切换为红色边框样式

    参数：
        event: 事件对象，从中获取当前 Entry
        original_style: 控件原始样式名，若为 None 或空则使用默认的样式
        validate_func: 接收 str 返回 bool 的验证函数
    """
    entry = event.widget
    original_style=original_style or 'default'

    if not validate_func(entry.get()):
        entry.configure(bootstyle="danger")
    else:
        entry.configure(bootstyle=original_style)

def comm_entry_validate(entry:tkinter.Entry, validate_func: Callable[[str], bool], original_style: str=None):
    """
    绑定 Entry 控件的事件，验证不合格则显示红框
    :param entry: 对应 Entry 控件
    :param validate_func: 验证函数，接收 str 返回 bool
    :param original_style: 该控件的原始样式名，若为 None 或空则使用默认的样式
    """
    validate_handler =functools.partial(entry_event_validate,validate_func=validate_func,original_style=original_style)
    entry.bind("<Enter>", validate_handler)
    entry.bind("<Leave>", validate_handler)
    entry.bind("<FocusOut>", validate_handler)
    entry.bind("<FocusIn>", validate_handler)
    entry.bind("<KeyRelease>", validate_handler)


