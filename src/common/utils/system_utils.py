"""系统工具模块。

此模块提供了系统相关的工具函数，包括：
- 进程管理和检测
- PyInstaller环境检测
- 程序重启功能
- Windows防火墙规则管理
"""

import logging
import os
import subprocess
import sys
from typing import Set

import psutil

from common import constants
from common import utils
from common.utils import logging_utils

logger = logging.getLogger(__name__)


def get_active_pids() -> Set[int]:
    """获取系统活动PID
    
    一般不使用该函数,其会检测所有并创建Process对象,造成资源浪费
    如果需要判断对应进程是否存在,请使用psutil.pid_exists(pid)
    
    Returns:
        Set[int]: 活动进程ID集合
    """
    active_pids = set()
    for proc in psutil.process_iter(attrs=['status']):
        status = proc.info.get('status')  # type: ignore
        # 过滤僵尸进程
        if status and status != psutil.STATUS_ZOMBIE:
            active_pids.add(proc.pid)

    return active_pids


def is_pyinstaller_bundle() -> bool:
    """检测是否在 PyInstaller 打包环境中运行
    
    Returns:
        bool: 是否在PyInstaller环境中
    """
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')


def restart_cur_program():
    """重启当前程序"""
    # 执行清除操作
    utils.shutdown_exec_funct.execute_registered_functions()
    executable = sys.executable
    args = sys.argv if is_pyinstaller_bundle() else [executable] + sys.argv
    logging_utils.logger_print(msg=f"restarting current program with args: {args}", custom_logger=logger)
    if constants.common_constants.IS_WINDOWS:
        subprocess.Popen(args, cwd=os.getcwd(), creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NEW_PROCESS_GROUP)
    else:
        os.execv(executable, args)
    sys.exit(0)


def remove_firewall_rule_with_windows(rule_name: str):
    """在Windows环境下删除指定防火墙规则
    
    Args:
        rule_name: 防火墙规则名称
    """
    if not constants.common_constants.IS_WINDOWS:
        return
    
    cmd = [
        "netsh", "advfirewall", "firewall", "delete", "rule",
        f"name={rule_name}"
    ]
    result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
    if result.returncode != 0:
        logging_utils.logger_print(msg=f"删除指定防火墙规则失败: {rule_name}, {result.stderr}", custom_logger=logger, log_level=logging.ERROR)
