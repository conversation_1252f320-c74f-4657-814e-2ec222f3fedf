"""GUI组件扩展模块。

此模块提供了基于ttkbootstrap的自定义GUI组件，包括：
- 精确浮点数仪表盘
- 自定义对话框
- 表格组件扩展
- 其他GUI工具组件
"""

import functools
import tkinter
from typing import Callable, Optional

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.utils import gui_utils


class PrecisionMeter(ttkb.Meter):
    """支持浮点数显示的精确仪表盘组件。

    由于ttkbootstrap的Meter其内部使用的int，会将外部传入的数字直接截断，
    转换成整数，无法显示小数点，因此在其基础上进行修改，补充浮点数数据保存功能。
    """

    def __init__(
        self,
        master=None,
        bootstyle=DEFAULT,
        arcrange=None,
        arcoffset=None,
        amounttotal: float = 100.0,  # 使用浮点数
        amountused: float = 0.0,     # 使用浮点数
        amountformat: str = "{:.1f}",
        wedgesize: int = 0,
        metersize: int = 200,
        metertype=FULL,
        meterthickness: int = 10,
        showtext: bool = True,
            interactive=False,
            stripethickness=0,
            textleft=None,
            textright=None,
            textfont="-size 20 -weight bold",
            subtext=None,
            subtextstyle=DEFAULT,
            subtextfont="-size 10",
            stepsize=1,
            **kwargs,
    ):

        # 使用临时整数值调用父类初始化
        super().__init__(
            master=master,
            bootstyle=bootstyle,
            arcrange=arcrange,
            arcoffset=arcoffset,
            amounttotal=int(amounttotal),  # 临时使用整数
            amountused=int(amountused),    # 临时使用整数
            amountformat=amountformat,
            wedgesize=wedgesize,
            metersize=metersize,
            metertype=metertype,
            meterthickness=meterthickness,
            showtext=showtext,
            interactive=interactive,
            stripethickness=stripethickness,
            textleft=textleft,
            textright=textright,
            textfont=textfont,
            subtext=subtext,
            subtextstyle=subtextstyle,
            subtextfont=subtextfont,
            stepsize=stepsize,
            **kwargs
        )

        self.amountusedvar = tkinter.DoubleVar(value=amountused)
        self.amounttotalvar = tkinter.DoubleVar(value=amounttotal)

        self.amountuseddisplayvar.set(self._get_meter_show_str(amountused))

        # 重新绑定更新事件
        self.amountusedvar.trace_add("write", self._update_meter)
    def _update_meter(self, *_):
        self._draw_meter()
        amount_used = self.amountusedvar.get()
        self.amountuseddisplayvar.set(self._get_meter_show_str(amount_used))

    def _get_meter_show_str(self, amount_used: float):
        """计算仪表盘中显示的文字内容规则:
        当前规则是:如果对于数字是0则显示"--"，否则显示格式化后的数字
        """
        if amount_used < 1e-9:
            amount_used=0
        return self._amountformat.format(amount_used)
    
    def _meter_value(self) -> float:
        """Calculate the arc length for floating point meter values."""
        return (
                (self.amountusedvar.get() / self.amounttotalvar.get()) * self._arcrange
                + self._arcoffset
        )

class Marquee(ttkb.Frame):
    def __init__(self, parent, text, fps=30, step=2, font_family_size:tuple[str, int]=("TkDefaultFont", 10), **kwargs):
        super().__init__(parent, **kwargs)
        self.fps = fps
        self.step = step
        self.font = font_family_size
        font_family, font_size = font_family_size
        self.canvas_height = font_size + 8

        style = ttkb.Style()
        light_color = style.colors.light

        self.canvas = tkinter.Canvas(
            self,
            height=self.canvas_height,
            highlightthickness=0,
            bg=light_color
        )
        self.canvas.pack(fill=X, side=LEFT, expand=True)
        self.canvas.bind("<Configure>", self._on_canvas_configure)

        self.text_id = self.canvas.create_text(
            self.canvas.winfo_reqwidth(),
            self.canvas_height // 2,
            text=text,
            anchor=W,
            font=font_family_size,
            )

        self.after(100, self._reset_and_animate) # noqa

    def _on_canvas_configure(self, event):
        """当画布尺寸变化时，重置文字位置到右侧"""
        if event.width > 10:
            self.canvas.coords(self.text_id, event.width, self.canvas_height // 2)

    def _reset_and_animate(self):
        if self.canvas.winfo_width() > 10:
            self.canvas.coords(self.text_id, self.canvas.winfo_width(), self.canvas_height // 2)
        self._animate()

    def _animate(self):
        self.canvas.move(self.text_id, -self.step, 0)
        x1, y1, x2, y2 = self.canvas.bbox(self.text_id)
        if x2 < 0:
            canvas_w = self.canvas.winfo_width()
            self.canvas.coords(self.text_id, canvas_w, self.canvas_height // 2)
        self.after(int(1000 / self.fps), self._animate) # noqa

class NotificationBar(ttkb.Frame):
    def __init__(self, parent, text, font_family_size:tuple[str, int], **kwargs):
        super().__init__(parent, **kwargs)
        self.marquee = Marquee(self, text=text, font_family_size=font_family_size)
        self.marquee.pack(side=LEFT, fill=X, expand=True, padx=(5, 0), pady=2)

        self.close_btn = ttkb.Checkbutton(
            self,
            text="x",
            command=self._on_close,
            bootstyle="danger-outline-toolbutton" # noqa
        )
        self.close_btn.configure(padding=(0,0))
        self.close_btn.pack(side=RIGHT, padx=2, pady=2)

    def _on_close(self):
        self.pack_forget()

class RowHoverTooltip:
    """
    为TreeView的行添加简单的tooltip功能
    treeview行显示提示文本
    """
    def __init__(self, treeview: ttkb.Treeview,font_family:str,font_size:int):
        self.treeview = treeview
        self.row_tooltips = {}  # 存储每行的tooltip文本
        self.tooltip_visible = False  # 跟踪tooltip是否可见
        self.tooltip_after_id = None  # 存储延迟显示的after ID

        # 确保使用正确的父窗口
        self.root = treeview.winfo_toplevel()

        # 创建独立的顶层窗口作为tooltip，而不是普通标签
        self.tooltip_window = tkinter.Toplevel(self.root)
        self.tooltip_window.withdraw()  # 初始隐藏
        self.tooltip_window.overrideredirect(True)  # 移除窗口边框
        self.tooltip_window.wm_attributes("-topmost", True)  # 保持在最上层

        # 在tooltip窗口中创建标签
        self.tooltip_label = tkinter.Label(
            self.tooltip_window,
            background="lightyellow",
            relief="solid",
            borderwidth=1,
            font=(font_family, font_size),
            justify='left',
            padx=5,
            pady=3
        )
        self.tooltip_label.pack()

        # 绑定鼠标移动和离开事件
        self.treeview.bind("<Motion>", self.on_motion)
        self.treeview.bind("<Leave>", self.on_leave)
        # 添加绑定处理滚动
        self.treeview.bind("<MouseWheel>", self.on_leave)
        self.treeview.bind("<Button-4>", self.on_leave)
        self.treeview.bind("<Button-5>", self.on_leave)
        # 添加焦点变化事件
        self.treeview.bind("<FocusOut>", self.on_leave)

        self.last_item = None  # 记录上一次鼠标移动到的行

    def set_row_tooltip(self, item_id, tooltip_text):
        """为指定行设置tooltip文本"""
        # 确保键是字符串类型，与TreeView的identify_row返回类型一致
        self.row_tooltips[str(item_id)] = tooltip_text

    def clear_all_row_tooltips(self):
        """清除所有行的tooltip"""
        self.row_tooltips.clear()

    def on_motion(self, event):
        """鼠标移动事件处理"""
        # 取消之前的延迟显示
        if self.tooltip_after_id:
            self.treeview.after_cancel(self.tooltip_after_id)
            self.tooltip_after_id = None

        # 获取当前鼠标下的行
        item = self.treeview.identify_row(event.y)

        if not item or item not in self.row_tooltips:
            self.hide_tooltip()
            self.last_item = None
            return

        if item == self.last_item and self.tooltip_visible:
            return  # 鼠标移动到同一行且tooltip已显示，忽略

        self.last_item = item

        # 隐藏之前的tooltip
        self.hide_tooltip()

        # 延迟300毫秒显示tooltip，避免快速移动时频繁显示
        tooltip_text = self.row_tooltips[item]
        if tooltip_text:  # 确保有文本才显示
            show_text=functools.partial(self.show_tooltip,event=event,text=tooltip_text)
            self.tooltip_after_id = self.treeview.after(300, show_text) # noqa

    def on_leave(self, event=None): # noqa
        """鼠标离开TreeView时隐藏tooltip"""
        # 取消任何待处理的tooltip显示
        if self.tooltip_after_id:
            self.treeview.after_cancel(self.tooltip_after_id)
            self.tooltip_after_id = None

        self.hide_tooltip()
        self.last_item = None

    def show_tooltip(self, event, text):
        """显示tooltip"""
        if not text:
            return

        # 设置tooltip文本
        self.tooltip_label.config(text=text)

        # 更新窗口以确保获得正确尺寸
        self.tooltip_window.update_idletasks()

        # 计算位置（相对于屏幕）
        x = event.x_root + 15
        y = event.y_root + 10

        # 防止tooltip超出屏幕
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        tooltip_width = self.tooltip_window.winfo_reqwidth()
        tooltip_height = self.tooltip_window.winfo_reqheight()

        # 水平方向调整
        if x + tooltip_width > screen_width:
            x = screen_width - tooltip_width - 10

        # 垂直方向调整
        if y + tooltip_height > screen_height:
            y = screen_height - tooltip_height - 10

        # 设置tooltip窗口位置并显示
        self.tooltip_window.geometry(f"+{x}+{y}")
        self.tooltip_window.deiconify()
        self.tooltip_visible = True

    def hide_tooltip(self):
        """隐藏tooltip"""
        self.tooltip_window.withdraw()
        self.tooltip_visible = False

class TreeviewWithFixedGrid(ttkb.Treeview):
    # 列定义字典键
    COL_ID_KEY='column_id'
    DISPLAY_TEXT_KEY='display_text'
    WIDTH_KEY='column_width'
    def __init__(self, parent, columns:list[dict[str,any]],font_family:str,font_size:int, need_tree=False, row_height=25, odd_bg='white', even_bg='#E8E8E8', **kw):
        """
        创建一个带固定列宽和网格线的 Treeview。

        :param parent:        父容器 (Frame 或其他)
        :param columns:       列定义列表，每个元素包含 (column_id, display_text, column_width) 其中column_id必须是唯一的
        :param need_tree:     是否需要树形结构 如果需要,那么必须包含column_id='#0',如果不需要则不能包含column_id='#0'
        :param row_height:    行高
        :param odd_bg:        奇数行背景色
        :param even_bg:       偶数行背景色
        :param kw:            Treeview使用的其他参数
        """
        self.columns = columns
        self.need_tree = need_tree
        # 在need_tree可能会在#0lie中写入图片,如果写入图片就需要缓存图片对象,避免垃圾回收
        self.images=[]
        self.column_ids=[]
        self._check_get_column_ids()
        super().__init__(parent, columns=self.column_ids, show='tree headings' if self.need_tree else 'headings',**kw)

        self.row_height = row_height
        self.font_family = font_family
        self.font_size = font_size
        self.header_height = 0  # 表头高度，初始化为0,需要在第一次绘制完成后计算
        self._set_style()

        # 行背景色标签
        self.tag_configure('oddrow', background=odd_bg)
        self.tag_configure('evenrow', background=even_bg)

        self.separators = [ttkb.Separator(self, orient='vertical')] # 表格竖线
        self.h_separators = []  # 表格横线 需要根据实时行数据动态创建 = row_size+2
        # 列头与列宽固定
        # 计算总宽度
        # 创建分隔符列表 - 现在每列右侧都有竖线（包括最后一列） col_id, display_text, width
        total_width=0
        for col_dict in self.columns:
            col_id, display_text, width = col_dict[TreeviewWithFixedGrid.COL_ID_KEY], col_dict[TreeviewWithFixedGrid.DISPLAY_TEXT_KEY], col_dict[TreeviewWithFixedGrid.WIDTH_KEY]
            self.heading(col_id, text=display_text)
            self.column(col_id, width=width, minwidth=width, stretch=NO, anchor=CENTER)
            self.separators.append(ttkb.Separator(self, orient='vertical'))
            total_width+=width

        self.total_width = total_width

        # 绑定禁止列调整
        self.bind('<Button-1>', self._prevent_resize_click)
        self.bind('<B1-Motion>', self._prevent_resize_drag)
        # 绑定更新
        parent.bind('<Configure>', self._update_separators_event)
        if isinstance(parent, (tkinter.Frame, ttkb.Frame)):
            parent.bind('<Configure>', self._update_separators_event)
        self.after(50, self._compute_header_height_once)  # noqa 延迟执行，确保绘制完成之后获取表头高度

    def _check_get_column_ids(self):
        """初始化时检测列ID:唯一性,need_tree-是否包含#0列"""
        # 是否包含#0列的定义
        define_col_id_0 = False
        # 提取列ID列表用于Treeview初始化
        for col_dict in self.columns:
            col_id=col_dict[TreeviewWithFixedGrid.COL_ID_KEY]
            if col_id in self.column_ids:
                raise ValueError(f'Duplicate column_id: {col_id}')

            if col_id == '#0':
                if define_col_id_0:
                    raise ValueError('Duplicate column_id: #0')
                define_col_id_0 = True
            else:
                self.column_ids.append(col_id)

        if self.need_tree and not define_col_id_0:
            """列定义中必须包含column_id=#0列，用于显示树形结构"""
            raise ValueError('All columns must contain a column with column_id="#0" for displaying tree structure.')
        if not self.need_tree and define_col_id_0:
            """列定义中不能包含column_id=#0列，除非need_tree=True"""
            raise ValueError('Column with column_id="#0" is not allowed when need_tree=False.')

    def _set_style(self):
        """设置样式"""
        # 样式配置
        style = ttkb.Style()
        style.configure("FixedGrid.Treeview", rowheight=self.row_height)
        style.configure("FixedGrid.Treeview.Heading", font=(self.font_family, self.font_size+1, 'bold'), anchor='center')
        style.configure("FixedGrid.Treeview", font=(self.font_family, self.font_size), anchor='center')  # 内容居中
        style.layout("FixedGrid.Treeview", [('Treeview.treearea', {'sticky': 'nswe'})])
        self.configure(style="FixedGrid.Treeview")
    def _compute_header_height_once(self):
        rows = self.get_children()
        temp_item = None
        if not rows:
            temp_item = self.insert('', 'end', values=[""] * len(self.column_ids))
        self.update_idletasks()

        sample_item = temp_item or rows[0]
        bbox_info = self.bbox(sample_item)
        if bbox_info:
            self.header_height = bbox_info[1]  # 获取首行起点y坐标
        if temp_item:
            self.delete(temp_item)

        # 计算完成后刷新一次分隔线（可选）
        self.update_separators()

    def _prevent_resize_click(self, event):
        if self.identify_region(event.x, event.y) == 'separator':
            return 'break'

    def _prevent_resize_drag(self, event):
        if self.identify_region(event.x, event.y) == 'separator':
            return 'break'
    def _update_separators_event(self, event): # noqa
        """在父容器大小变化时更新表格分隔线"""
        self.after(50, self.update_separators)  # noqa 延迟执行，确保绘制完成

    def _get_cur_last_row_height(self)->int:
        """获取当前最后一行的高度"""
        rows = self.get_children()
        row_count = len(rows)
        if row_count > 0:
            cur_row_y = self.yview()
            # 获取最后一行的位置
            last_item = rows[-1]
            self.see(last_item)  # 👈 强制滚动到可视区域
            self.update_idletasks()  # 👈 确保布局刷新
            last_item_bbox = self.bbox(last_item)
            self.yview_moveto(cur_row_y[0])  # 👈 恢复滚动位置
            if last_item_bbox:
                # content_height = 最后一行的y坐标 + 行高
                return last_item_bbox[1] + self.row_height
        return  self.header_height

    def update_separators(self, event=None): # noqa
        """更新表格分隔线"""
        if self.header_height==0:
            return  # 尚未计算表头高度，先延迟执行
        # 获取当前行数
        rows = self.get_children()
        row_count = len(rows)

        # 计算需要显示的横线数量
        # 表头上下方的横线 (2条) + 每行数据下方的横线 (row_count条)
        needed = row_count + 2

        # 计算内容高度（最后一行的y坐标 + 行高）
        cur_last_row_height = self._get_cur_last_row_height()

        # 绘制竖线：每列右侧都有竖线（包括最后一列）
        x = 0
        self.separators[0].place(x=0, y=0, height=cur_last_row_height, anchor=NW) # 表头最左侧的竖线
        for i, col_dict in enumerate(self.columns,start=1):
            width =  col_dict[TreeviewWithFixedGrid.WIDTH_KEY]
            # 每列右侧都绘制竖线
            self.separators[i].place(x=x+width, y=0, height=cur_last_row_height, anchor=NW)
            x += width

        # 确保横线分隔符数量足够
        if len(self.h_separators) < needed:
            for _ in range(needed - len(self.h_separators)):
                self.h_separators.append(ttkb.Separator(self, orient='horizontal'))

        # 绘制横线：包括表头下方和每行数据下方
        y = self.header_height  # 第一条横线在表头下方
        self.h_separators[0].place(x=0, y=0, width=self.total_width)  # 表头最上方的横线
        for i in range(1,needed):
            # 设置横线宽度为表格内容总宽度，不超过表格内容区域
            self.h_separators[i].place(x=0, y=y, width=self.total_width)
            y += self.row_height

        # 隐藏多余的横线
        for i in range(needed, len(self.h_separators)):
            self.h_separators[i].place_forget()

    def add_rows(self, data_list:list[dict[str,any]], row_processor: Optional[Callable[["TreeviewWithFixedGrid", str, dict[str, any], int, str], None]]=None,need_update_separators=True):
        """
        批量添加多行数据，并更新分隔线。

        :param data_list: 可迭代对象，其内部元素需要和列id一一对应，格式为 {col_id1: value1, col_id2: value2,...},其顺序可以不定
        :param row_processor: 可选的自定义行处理函数，格式为:
            def processor(treeview, item_id, row_data, index, tag):
                # treeview: 当前Treeview实例
                # item_id: 自动生成的项ID
                # row_data: 当前行数据
                # index: 当前行索引（从0开始）
                # tag: 建议的行标签（奇偶行）
                # 可以在这里设置额外的属性或标签
        :param need_update_separators: 是否需要更新分隔线，默认True
        """
        start = len(self.get_children())
        for idx, row in enumerate(data_list, start=start):
            # 对应列插入对应值
            values=tuple(row.get(col_id, '') for col_id in self.column_ids)
            text=''
            if self.need_tree:
                text = row['#0']
            tag = 'evenrow' if idx % 2 == 0 else 'oddrow'
            item_id = self.insert('', idx, values=values, text=text, tags=(tag,))
            # 如果有自定义行处理函数，则调用它
            if row_processor:
                try:
                    row_processor(self, item_id, row, idx, tag)
                except TypeError as e:
                    err_msg=f"行处理器调用失败: {str(e)}"
                    # server_utils.logger_print(err_msg,custom_logger=lo)
                    raise RuntimeError(err_msg) from e

        # 更新网格线
        if need_update_separators:
            self.update_separators()

    def clear_all_data(self):
        """清除所有数据"""
        for item in self.get_children():
            self.delete(item)
        self.update_separators()

class TooltipLabel(ttkb.Label):
    """悬浮提示标签:只要鼠标悬浮在当前控件范围内就显示提示内容[content_func---自定义悬浮框]，否则隐藏提示内容。"""
    def __init__(self, parent, content_func, position="below", offset_x=0, offset_y=0, **kwargs):
        super().__init__(parent, **kwargs)
        self.content_func = content_func
        self.position = position
        self.offset_x = offset_x
        self.offset_y = offset_y
        self.parent = parent

        self._popup = None
        self._active = False
        self._after_id = None  # ✅ 防止重复触发 after

        self.bind("<Enter>", self._on_enter)

    def _create_popup(self):
        if self._popup and self._popup.winfo_exists():
            return self._popup  # ✅ 已存在，避免重复创建

        popup = tkinter.Toplevel(self.parent)
        popup.overrideredirect(True)
        content_frame = tkinter.Frame(popup)
        content_frame.pack(padx=1, pady=1)
        self.content_func(content_frame)
        self._popup = popup
        return popup

    def _position_popup(self):
        if not self._popup or not self._popup.winfo_exists():
            return
        x, y = self.winfo_rootx(), self.winfo_rooty()
        w, h = self.winfo_width(), self.winfo_height()
        self._popup.update_idletasks()  # ✅ 更新尺寸
        pw, ph = self._popup.winfo_width(), self._popup.winfo_height()
        ox, oy = self.offset_x, self.offset_y
        pos = self.position
        if pos == 'above': px, py = x + ox, y - ph + oy
        elif pos == 'below': px, py = x + ox, y + h + oy
        elif pos == 'left': px, py = x - pw + ox, y + oy
        elif pos == 'right': px, py = x + w + ox, y + oy
        elif pos == 'top-left': px, py = x + ox, y - ph + oy
        elif pos == 'top-right': px, py = x + w - pw + ox, y - ph + oy
        elif pos == 'bottom-left': px, py = x + ox, y + h + oy
        elif pos == 'bottom-right': px, py = x + w - pw + ox, y + h + oy
        else: px, py = x + ox, y + h + oy
        self._popup.geometry(f'+{int(px)}+{int(py)}')

    def _show_popup(self):
        if not self._active:
            self._create_popup()
            self._position_popup()
            self._popup.deiconify()
            self._active = True
        self._start_delayed_hide()  # ✅ 每次悬浮都需要重新判断，但不重复绑定

    def _hide_popup(self):
        if self._popup and self._popup.winfo_exists():
            self._popup.withdraw()
        self._active = False
        if self._after_id:
            self.after_cancel(self._after_id)
            self._after_id = None  # ✅ 清除状态避免悬挂

    def _on_enter(self, event):  # noqa
        self._show_popup()

    def _start_delayed_hide(self):
        if self._after_id:
            self.after_cancel(self._after_id)  # ✅ 避免多次 after
        self._after_id = self.after(300, self._delayed_hide)  # noqa ⏱️ 可调频率，默认 300ms

    def _delayed_hide(self):
        if not gui_utils.is_mouse_inside_widget(self):  # ✅ 只判断 Label，不判断浮窗
            self._hide_popup()
        else:
            self._start_delayed_hide()  # ✅ 继续等待
